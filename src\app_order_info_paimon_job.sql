-- Flink SQL Job for app_orderInfo to Paimon

-- Catalog and Environment Configuration
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

-- To simplify debugging "Unsupported SQL", you can temporarily comment out Hive Catalog if not strictly needed for this specific job.
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;

SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'hh_slfn2_sschj';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- Kafka Source Table for app_orderInfo
-- DDL revised to capture all distinct field names and types observed in '样例数据.txt'
CREATE TEMPORARY TABLE ods_kafka_app_orderinfo_source (
  -- Fields from JSON (original casing used as DDL field name for direct mapping)
  -- These are potential fields; 'json.ignore-parse-errors' = 'true' handles missing ones.
  effective_time STRING,
  transId STRING,
  city_code STRING,
  stlogin_mobile STRING,    -- from JSON "stlogin_mobile"
  province_code STRING,
  province_name STRING,
  qkactId STRING,
  city_name STRING,
  registertime STRING,      -- from JSON "registertime" (consistent)
  product_name_cb STRING,
  order_type STRING,
  register_time STRING,     -- from original DDL, assumed still possible

  -- Fields with snake_case vs camelCase variations or type variations
  `order_code` STRING,        -- from JSON "order_code"
  `orderCode` STRING,         -- from JSON "orderCode"

  `order_status` STRING,      -- from JSON "order_status"
  `orderStatus` STRING,       -- from JSON "orderStatus"

  `product_id_busi` STRING,   -- from JSON "product_id_busi"
  `productIdBusi` STRING,     -- from JSON "productIdBusi"

  `product_name_busi` STRING, -- from JSON "product_name_busi"
  `productNameBusi` STRING,   -- from JSON "productNameBusi"

  `product_id_cb` STRING,     -- from JSON "product_id_cb"
  `productIdCb` STRING,       -- from JSON "productIdCb"

  `order_mobile` STRING,      -- from JSON "order_mobile"
  `orderMobile` STRING,       -- from JSON "orderMobile"

  `table_source` STRING,      -- from JSON "table_source"
  `tableSource` STRING,       -- from JSON "tableSource"
  
  `product_fee` DECIMAL(38, 18), -- for JSON "product_fee": 20 (numeric)
  `productFee` STRING          -- for JSON "productFee": "5.00" (string)

) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.58.131:39092,10.177.58.132:39092,10.177.58.133:39092,10.177.58.134:39092,10.177.58.135:39092,10.177.58.136:39092,10.177.58.137:39092,10.177.58.138:39092,10.177.58.139:39092,10.177.58.140:39092,10.177.58.141:39092,10.177.58.142:39092,10.177.58.143:39092,10.177.58.144:39092,10.177.58.145:39092,10.177.58.146:39092,10.177.58.147:39092,10.177.58.148:39092,10.177.58.149:39092,10.177.58.150:39092,10.177.58.154:39092,10.177.58.155:39092,10.177.58.156:39092,10.177.58.157:39092,10.177.58.158:39092,10.177.58.159:39092,10.177.58.151:39092,10.177.58.152:39092,10.177.58.153:39092',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.group.id' = 'app_orderinfo_group_example', -- ### PLEASE UPDATE THIS VALUE to a unique and stable group ID for this job ###
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="rt_data_system" password="Urt@ynjd07";',
  'topic' = 'realtime_app_orderinfo_example', -- ### PLEASE UPDATE THIS VALUE ###
  'format' = 'json',
  'json.ignore-parse-errors' = 'true', -- Handles missing fields or malformed JSON by setting fields to null
  'scan.startup.mode' = 'group-offsets' -- Recommended for continuous jobs.
);

-- Paimon Target Table: ods_r_paimon_zaixian_app_orderInfo (schema uses snake_case)
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_app_orderInfo` (
  register_time STRING,
  effective_time STRING,
  transId STRING,
  city_code STRING,
  product_fee STRING, -- Paimon target is STRING
  product_name_busi STRING,
  stlogin_mobile STRING,
  province_code STRING,
  product_id_busi STRING,
  province_name STRING,
  qkactId STRING,
  order_code STRING PRIMARY KEY NOT ENFORCED, -- Primary key for Paimon table
  order_status STRING,
  city_name STRING,
  registertime STRING,
  product_name_cb STRING,
  table_source STRING,
  product_id_cb STRING,
  order_mobile STRING,
  order_type STRING,
  paimon_time TIMESTAMP(3) COMMENT 'Timestamp of when the record was processed into Paimon'
) WITH (
  'connector' = 'paimon',
  'path' = 'hdfs:///paimon/ubd_sscj_prod_flink/ods_r_paimon_zaixian_app_orderInfo',
  'auto-create' = 'true',
  'merge-engine' = 'partial-update',
  'sequence.field' = 'paimon_time',
  'partial-update.ignore-delete' = 'true',
  'changelog-producer' = 'input',
  'snapshot.time-retained' = '1h' -- Example: Paimon snapshot retention
);

-- Insert Data from Kafka into Paimon, using COALESCE for variant JSON fields
INSERT INTO
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_app_orderInfo`
SELECT
  COALESCE(s.register_time, NULL) AS register_time,    -- Ensure all target columns are present in select
  COALESCE(s.effective_time, NULL) AS effective_time,
  COALESCE(s.transId, NULL) AS transId,
  COALESCE(s.city_code, NULL) AS city_code,
  CAST(COALESCE(CAST(s.`product_fee` AS STRING), s.`productFee`) AS STRING) AS product_fee,
  COALESCE(s.`product_name_busi`, s.`productNameBusi`) AS product_name_busi,
  COALESCE(s.stlogin_mobile, s.`orderMobile`) AS stlogin_mobile, -- Mapping orderMobile to stlogin_mobile if stlogin_mobile is null
  COALESCE(s.province_code, NULL) AS province_code,
  COALESCE(s.`product_id_busi`, s.`productIdBusi`) AS product_id_busi,
  COALESCE(s.province_name, NULL) AS province_name,
  COALESCE(s.qkactId, NULL) AS qkactId,
  COALESCE(s.`order_code`, s.`orderCode`) AS order_code, -- CRITICAL for Paimon Primary Key
  COALESCE(s.`order_status`, s.`orderStatus`) AS order_status,
  COALESCE(s.city_name, NULL) AS city_name,
  COALESCE(s.registertime, NULL) AS registertime, -- 'registertime' from JSON
  COALESCE(s.product_name_cb, NULL) AS product_name_cb,
  COALESCE(s.`table_source`, s.`tableSource`) AS table_source,
  COALESCE(s.`product_id_cb`, s.`productIdCb`) AS product_id_cb,
  COALESCE(s.`order_mobile`, s.`orderMobile`) AS order_mobile, -- Uses snake_case field `order_mobile` first
  COALESCE(s.order_type, NULL) AS order_type,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time
FROM
  ods_kafka_app_orderinfo_source s;
  -- You can add specific Kafka source options for this INSERT query if needed, overriding DDL:
  /*+ OPTIONS(
    'properties.group.id'='app_orderinfo_insert_specific_group', -- Example: override DDL group.id for this specific query
    'scan.startup.mode'='earliest-offset' -- Example: override DDL startup mode
  )*/ 