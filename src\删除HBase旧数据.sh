#!/bin/bash

# 脚本功能：每月1日删除HBase中指定列簇的旧数据（支持跨集群访问）
# 用法: ./删除HBase旧数据.sh [选项]，或直接执行无需任何参数

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -z, --zookeeper       ZooKeeper地址，格式为host:port[,host:port,...]"
    echo "  -t, --table           HBase表名"
    echo "  -u, --user            访问HBase的用户名 (默认: hbase)"
    echo "  -h, --hbase-home      HBase客户端安装目录 (默认: /usr/hdp/current/hbase-client)"
    echo "  -c, --column-family   要删除的列簇，多个列簇用逗号分隔 (默认: com1:SK000010)"
    echo "  -r, --znode-root      HBase在ZooKeeper中的根路径 (默认: /hbase)"
    echo "  -d, --debug           启用调试模式"
    echo "  -p, --port            ZooKeeper端口 (默认: 2181)"
    echo "  -l, --log-file        日志文件路径 (默认: /var/log/hbase_data_cleanup_DATE.log)"
    echo "  --test-connection     仅测试连接，不执行删除操作"
    echo "  --force               强制执行，忽略日期检查"
    echo "  --help                显示此帮助信息并退出"
    echo
    echo "示例:"
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" -c \"com1:SK000010\""
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" -c \"com1:SK000010,com1:SK000011\""
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" -r \"/hbase-secure\" --force"
    echo "  $0 --test-connection -z \"zk1,zk2,zk3\" -r \"/hbase-secure\""
    echo "  直接运行 $0 则使用脚本中的默认配置"
    exit 1
}

# 默认值 - 直接设置生产环境的默认值，无需用户输入参数
HBASE_HOME="/usr/hdp/current/hbase-client"
HBASE_USER="hbase"
# 设置您的目标表
TABLE_NAME="ctg810544697531_hh_fed_sub23_lclabel:label_lc_realtime_data_test"
# 设置您需要删除的列簇
COLUMN_FAMILY="com1:IS_CONTRACT_USER"
# 设置您的ZooKeeper地址
ZOOKEEPER_HOSTS="************,************,************"
ZNODE_ROOT="/hbase-unsecure"
DEBUG=false
ZK_PORT="2181"
TEST_CONNECTION=false
FORCE=false
LOG_FILE="/data/disk01/hh_slfn2_sschj/private/liucz37/shell/hbase/delete.log"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        -z|--zookeeper)
            ZOOKEEPER_HOSTS="$2"
            shift 2
            ;;
        -t|--table)
            TABLE_NAME="$2"
            shift 2
            ;;
        -u|--user)
            HBASE_USER="$2"
            shift 2
            ;;
        -h|--hbase-home)
            HBASE_HOME="$2"
            shift 2
            ;;
        -c|--column-family)
            COLUMN_FAMILY="$2"
            shift 2
            ;;
        -r|--znode-root)
            ZNODE_ROOT="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG=true
            shift
            ;;
        -p|--port)
            ZK_PORT="$2"
            shift 2
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        --test-connection)
            TEST_CONNECTION=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 函数: 记录日志
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a ${LOG_FILE}
}

# 格式化ZooKeeper地址
format_zk_quorum() {
    local hosts=$1
    local port=$2
    local result=""
    
    # 分割主机名并添加端口
    IFS=',' read -ra HOST_ARRAY <<< "$hosts"
    for i in "${HOST_ARRAY[@]}"; do
        # 检查主机名是否已包含端口
        if [[ $i == *":"* ]]; then
            result="${result}${i},"
        else
            result="${result}${i}:${port},"
        fi
    done
    
    # 移除最后的逗号
    result=${result%,}
    echo $result
}

# 检查ZooKeeper连接
check_zk_connection() {
    local zk_quorum=$1
    local znode=$2
    
    if [ -x "$(command -v zkCli.sh)" ]; then
        log "使用zkCli.sh检查ZooKeeper连接..."
        zkCli.sh -server $zk_quorum ls $znode
        return $?
    elif [ -x "$(command -v zookeeper-client)" ]; then
        log "使用zookeeper-client检查ZooKeeper连接..."
        zookeeper-client -server $zk_quorum ls $znode
        return $?
    elif [ -x "$HBASE_HOME/bin/hbase" ]; then
        log "使用HBase自带工具检查ZooKeeper连接..."
        $HBASE_HOME/bin/hbase zkcli -server $zk_quorum ls $znode
        return $?
    else
        log "警告: 未找到ZooKeeper客户端工具，无法测试连接"
        return 1
    fi
}

# 如果未指定ZooKeeper地址，使用默认值（之前已设置）
log "使用ZooKeeper地址: $ZOOKEEPER_HOSTS"

# 格式化ZooKeeper地址
ZOOKEEPER_QUORUM=$(format_zk_quorum "$ZOOKEEPER_HOSTS" "$ZK_PORT")
log "ZooKeeper地址: $ZOOKEEPER_QUORUM"

# 如果只是测试连接
if [ "$TEST_CONNECTION" = true ]; then
    log "测试与ZooKeeper的连接..."
    log "ZooKeeper地址: $ZOOKEEPER_QUORUM"
    log "ZNode根路径: $ZNODE_ROOT"
    check_zk_connection "$ZOOKEEPER_QUORUM" "$ZNODE_ROOT"
    exit $?
fi

# 检查是否为每月1日（除非强制执行）
if [ "$FORCE" != true ]; then
    CURRENT_DAY=$(date +%d)
    if [ "$CURRENT_DAY" != "01" ]; then
        log "今天不是每月1日，跳过执行。若要强制执行，请添加 --force 参数"
        exit 0
    fi
fi

# 检查HBase客户端是否存在
if [ ! -d "$HBASE_HOME" ]; then
    log "错误: HBase客户端目录不存在: $HBASE_HOME"
    exit 1
fi

# 创建临时配置文件
TEMP_CONF_DIR=$(mktemp -d)
log "创建临时配置目录: $TEMP_CONF_DIR"

cat > $TEMP_CONF_DIR/hbase-site.xml << EOF
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>hbase.zookeeper.quorum</name>
        <value>${ZOOKEEPER_QUORUM}</value>
    </property>
    <property>
        <name>zookeeper.znode.parent</name>
        <value>${ZNODE_ROOT}</value>
    </property>
    <property>
        <name>hbase.client.keyvalue.maxsize</name>
        <value>10485760</value>
    </property>
    <property>
        <name>hbase.rpc.timeout</name>
        <value>600000</value>
    </property>
    <property>
        <name>hbase.client.operation.timeout</name>
        <value>600000</value>
    </property>
    <property>
        <name>hbase.client.scanner.timeout.period</name>
        <value>600000</value>
    </property>
    <property>
        <name>zookeeper.session.timeout</name>
        <value>600000</value>
    </property>
    <property>
        <name>hbase.client.retries.number</name>
        <value>20</value>
    </property>
</configuration>
EOF

# 设置环境变量
export HBASE_HOME
export PATH=$HBASE_HOME/bin:$PATH
export HADOOP_USER_NAME=${HBASE_USER}
export HBASE_CONF_DIR=$TEMP_CONF_DIR

if [ "$DEBUG" = true ]; then
    log "调试信息:"
    log "  HBASE_HOME = $HBASE_HOME"
    log "  HBASE_CONF_DIR = $HBASE_CONF_DIR"
    log "  HADOOP_USER_NAME = $HADOOP_USER_NAME"
    log "  ZooKeeper = $ZOOKEEPER_QUORUM"
    log "  ZNode根路径 = $ZNODE_ROOT"
    log "  表名 = $TABLE_NAME"
    log "  列簇 = $COLUMN_FAMILY"
    log "  配置文件内容:"
    cat $TEMP_CONF_DIR/hbase-site.xml
    
    # 尝试列出表
    log "尝试列出HBase表:"
    $HBASE_HOME/bin/hbase shell << EOF
list
EOF
fi

# 获取上个月的日期
LAST_MONTH=$(date -d "last month" +%Y%m)
log "开始删除 ${LAST_MONTH} 之前的数据，表: ${TABLE_NAME}"

# 处理多个列簇
IFS=',' read -ra COLUMN_FAMILY_ARRAY <<< "$COLUMN_FAMILY"
log "要删除的列簇数量: ${#COLUMN_FAMILY_ARRAY[@]}"

for CF in "${COLUMN_FAMILY_ARRAY[@]}"; do
    log "处理列簇: $CF"

    # 创建HBase脚本来扫描数据
    TMP_SCRIPT="/tmp/hbase_cleanup_script_$(date +\%s)_${CF//:/}.txt"
    cat > ${TMP_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF}', RAW => true, VERSIONS => 1, CACHE => 10000}
EOF

    # 执行扫描获取所有rowkey
    log "扫描表格以获取包含旧数据的rowkey列表..."
    ROWKEYS=$($HBASE_HOME/bin/hbase shell ${TMP_SCRIPT} 2>&1 | grep "^row" | awk '{print $2}' | tr -d ',')

    # 删除操作
    if [ -n "$ROWKEYS" ]; then
        log "找到需要处理的数据，准备删除列簇 ${CF} 的数据..."
        DELETE_SCRIPT="/tmp/hbase_delete_script_$(date +\%s)_${CF//:/}.txt"
        
        # 批量删除
        echo "# 批量删除脚本" > ${DELETE_SCRIPT}
        echo "# 表名: ${TABLE_NAME}" >> ${DELETE_SCRIPT}
        echo "# 列簇: ${CF}" >> ${DELETE_SCRIPT}
        echo "# 生成时间: $(date)" >> ${DELETE_SCRIPT}
        echo "" >> ${DELETE_SCRIPT}
        
        echo "# 初始化批量删除数组" >> ${DELETE_SCRIPT}
        echo "deleteall_batch = []" >> ${DELETE_SCRIPT}
        
        # 添加每个rowkey到批量删除数组
        ROWKEY_COUNT=0
        BATCH_SIZE=10000
        CURRENT_BATCH=0
        
        for rowkey in $ROWKEYS; do
            echo "delete = org.apache.hadoop.hbase.client.Delete.new('$rowkey')" >> ${DELETE_SCRIPT}
            echo "delete.addFamily(Bytes.toBytes('${CF}'))" >> ${DELETE_SCRIPT}
            echo "deleteall_batch.push(delete)" >> ${DELETE_SCRIPT}
            ROWKEY_COUNT=$((ROWKEY_COUNT + 1))
            
            # 每10000个rowkey提交一次批量删除
            if [ $((ROWKEY_COUNT % BATCH_SIZE)) -eq 0 ]; then
                CURRENT_BATCH=$((CURRENT_BATCH + 1))
                echo "# 提交批次 $CURRENT_BATCH (${ROWKEY_COUNT}个rowkey)" >> ${DELETE_SCRIPT}
                echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
                echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
                echo "puts \"已删除 ${ROWKEY_COUNT} 个rowkey...\"" >> ${DELETE_SCRIPT}
                echo "deleteall_batch = []" >> ${DELETE_SCRIPT}
            fi
        done
        
        # 处理剩余的rowkey
        if [ $((ROWKEY_COUNT % BATCH_SIZE)) -ne 0 ]; then
            CURRENT_BATCH=$((CURRENT_BATCH + 1))
            echo "# 提交最后一个批次 $CURRENT_BATCH" >> ${DELETE_SCRIPT}
            echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
            echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
        fi
        
        echo "puts \"总共删除 ${ROWKEY_COUNT} 个rowkey的 ${CF} 列簇数据，完成!\"" >> ${DELETE_SCRIPT}
        echo "exit" >> ${DELETE_SCRIPT}
        
        # 如果调试模式，显示脚本内容
        if [ "$DEBUG" = true ]; then
            log "删除脚本内容:"
            cat ${DELETE_SCRIPT}
        fi
        
        log "执行删除操作..."
        $HBASE_HOME/bin/hbase shell ${DELETE_SCRIPT}
        
        DELETE_RESULT=$?
        if [ $DELETE_RESULT -eq 0 ]; then
            log "成功删除 ${TABLE_NAME} 表中 ${CF} 列簇的旧数据 (${ROWKEY_COUNT} 个rowkey)"
        else
            log "删除操作失败，退出码: $DELETE_RESULT"
            log "可能的问题:"
            log "1. ZooKeeper地址不正确"
            log "2. HBase的ZNode根路径不是 $ZNODE_ROOT (使用-r选项指定正确的路径)"
            log "3. 用户 $HBASE_USER 没有访问权限"
            log "4. 表 $TABLE_NAME 不存在"
            log
            log "建议:"
            log "1. 使用--test-connection选项测试ZooKeeper连接"
            log "2. 使用-d选项启用调试模式获取更多信息"
            exit 1
        fi
        
        # 清理临时文件
        rm -f ${DELETE_SCRIPT}
    else
        log "没有找到列簇 ${CF} 需要删除的数据"
    fi

    # 清理临时文件
    rm -f ${TMP_SCRIPT}
done

rm -rf ${TEMP_CONF_DIR}

log "清理操作完成"
exit 0 