-- 设置catalog
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);

CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);

-- 设置执行参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'hh_slfn2_sschj';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- 设置Paimon扫描参数（全局配置）
SET 'table.exec.source.idle-timeout' = '30s';

-- 使用hive catalog
USE CATALOG hive_catalog;

-- 创建Kafka目标表
CREATE TABLE IF NOT EXISTS dwa_r_kafka_bbgx_011 (
    id STRING,
    user_id STRING,
    manager_id STRING,
    is_vip STRING,
    is_main STRING,
    is_recover STRING,
    customer_mobile_fnv STRING,
    customer_mobile STRING,
    update_id STRING,
    update_time STRING,
    province_code STRING,
    create_id STRING,
    create_time STRING
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'CMW_CUST_MANAGER',
  'key.format' = 'json',
  'key.fields' = 'user_id',
  'value.format' = 'json',
  'key.json.ignore-parse-errors' = 'true',
  'key.json.fail-on-missing-field' = 'false',
  'value.json.ignore-parse-errors' = 'true',
  'value.json.fail-on-missing-field' = 'false'
);

-- 使用paimon catalog进行查询
USE CATALOG paimon_catalog;

-- 核心处理逻辑
INSERT INTO hive_catalog.ubd_sscj_prod_flink.dwa_r_kafka_bbgx_011
SELECT
    ref.id,
    ref.user_id,
    ref.manager_id,
    ref.is_vip,
    ref.is_main,
    ref.is_recover,
    ref.customer_mobile_fnv,
    ref.customer_mobile,
    ref.update_id,
    ref.update_time,
    usr.PROVINCE_CODE AS province_code,
    ref.create_id,
    ref.create_time
FROM
    ods_r_paimon_cmw_cust_manager_ref AS ref
INNER JOIN
    ods_r_paimon_tf_f_user AS usr
ON
    ref.user_id = usr.USER_ID
WHERE
    usr.PROVINCE_CODE = '11' OR usr.PROVINCE_CODE = '011';
