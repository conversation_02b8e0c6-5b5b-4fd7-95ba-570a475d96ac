-- 1. 创建新的Paimon表
CREATE TABLE IF NOT EXISTS dwd_r_paimon_user_info_f (
    USER_ID STRING,
    IN_DATE STRING,
    OPEN_DATE STRING,
    DESTROY_TIME STRING,
    PRIMARY KEY (USER_ID) NOT ENFORCED
);

-- 2. 从ods_r_paimon_tf_f_user表中筛选user_id并插入新表
INSERT INTO dwd_r_paimon_user_info_f (USER_ID)
SELECT
    USER_ID
FROM
    ods_r_paimon_tf_f_user
WHERE
    LENGTH(OPEN_DATE) = 12;

-- 3. 从dwa_r_paimon_human_user_wide表中获取字段信息并更新到新表
MERGE INTO dwd_r_paimon_user_info_f t1
USING dwa_r_paimon_human_user_wide t2 ON t1.USER_ID = t2.USER_ID
WHEN MATCHED THEN
    UPDATE SET
        t1.IN_DATE = t2.IN_DATE,
        t1.OPEN_DATE = t2.OPEN_DATE,
        t1.DESTROY_TIME = t2.DESTROY_TIME;

-- 4. 将新表中的数据更新到ods_r_paimon_tf_f_user表
MERGE INTO ods_r_paimon_tf_f_user t1
USING dwd_r_paimon_user_info_f t2 ON t1.USER_ID = t2.USER_ID
WHEN MATCHED THEN
    UPDATE SET
        t1.IN_DATE = t2.IN_DATE,
        t1.OPEN_DATE = t2.OPEN_DATE,
        t1.DESTROY_TIME = t2.DESTROY_TIME;
