-- StreamPark上执行的FlinkSQL
-- 设置执行参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'ctg782612549842_hh_fed_sub15_sssc_prod15';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = '${slfn2-hdfs-hh_slfn2_sschj}/paimon', 
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

--paimon表-话费券
CREATE TABLE  IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_HeFei_product_info` (
  `USER_ID` STRING NOT NULL,
  `SERIAL_NUMBER` STRING NOT NULL,
  `PROVINCE_CODE` STRING NOT NULL,
  `PRODUCT_ID` STRING NOT NULL,
  `START_DATE` STRING,
  `END_DATE` STRING,
  `PAIMON_TIME` TIMESTAMP(3),
  CONSTRAINT `PK_USER_PRODUCT` PRIMARY KEY (`USER_ID`, `PRODUCT_ID`) NOT ENFORCED
) WITH (
  'bucket' = '64',
  'write-buffer-size' = '256mb',
  'write-buffer-spillable' = 'true',
  'num-levels' = '5',
  'snapshot.time-retained' = '6h',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h',
  'ttl' = '30d'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` (
  PARTITION_ID STRING,
  USER_ID STRING,
  CUST_ID STRING,
  USECUST_ID STRING,
  BRAND_CODE STRING,
  PRODUCT_ID STRING,
  EPARCHY_CODE STRING,
  CITY_CODE STRING,
  USER_PASSWD STRING,
  USER_DIFF_CODE STRING,
  USER_TYPE_CODE STRING,
  SERIAL_NUMBER STRING,
  NET_TYPE_CODE STRING,
  SCORE_VALUE STRING,
  CREDIT_CLASS STRING,
  BASIC_CREDIT_VALUE STRING,
  CREDIT_VALUE STRING,
  ACCT_TAG STRING,
  PREPAY_TAG STRING,
  IN_DATE STRING,
  OPEN_DATE STRING,
  OPEN_MODE STRING,
  OPEN_DEPART_ID STRING,
  OPEN_STAFF_ID STRING,
  IN_DEPART_ID STRING,
  IN_STAFF_ID STRING,
  REMOVE_TAG STRING,
  DESTROY_TIME STRING,
  REMOVE_EPARCHY_CODE STRING,
  REMOVE_CITY_CODE STRING,
  REMOVE_DEPART_ID STRING,
  REMOVE_REASON_CODE STRING,
  PRE_DESTROY_TIME STRING,
  FIRST_CALL_TIME STRING,
  LAST_STOP_TIME STRING,
  USER_STATE_CODESET STRING,
  MPUTE_MONTH_FEE STRING,
  MPUTE_DATE STRING,
  UPDATE_TIME STRING,
  ASSURE_CUST_ID STRING,
  ASSURE_TYPE_CODE STRING,
  ASSURE_DATE STRING,
  DEVELOP_STAFF_ID STRING,
  DEVELOP_DATE STRING,
  DEVELOP_EPARCHY_CODE STRING,
  DEVELOP_CITY_CODE STRING,
  DEVELOP_DEPART_ID STRING,
  DEVELOP_NO STRING,
  REMARK STRING,
  CREDIT_RULE_ID STRING,
  CONTRACT_ID STRING,
  CHANGEUSER_DATE STRING,
  IN_NET_MODE STRING,
  PRODUCT_TYPE_CODE STRING,
  MAIN_DISCNT_CODE STRING,
  PRODUCT_SPEC STRING,
  PROVINCE_CODE STRING,
  OPT STRING,
  OPTTIME STRING,
  CDHTIME STRING,
  DATASOURCE STRING,
  IN_TIME STRING,
  DATABASE_TAG STRING,
  EVENT_TIME TIMESTAMP(3) NOT NULL COMMENT '事件时间戳',
  KAFKA_TIME TIMESTAMP(3) NOT NULL COMMENT 'kafka时间戳',
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  HEADERS MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
  'bucket' = '256', 
  'bucket-key' = 'USER_ID',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'write-only' = 'true',
  'snapshot.time-retained' = '1h'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` (
  PARTITION_ID STRING,
  USER_ID STRING,
  PRODUCT_MODE STRING,
  PRODUCT_ID STRING,
  BRAND_CODE STRING,
  START_DATE STRING,
  END_DATE STRING,
  ITEM_ID STRING,
  USER_ID_A STRING,
  PROD_ITEM_ID STRING,
  PRIMARY_USER_ID STRING,
  UPDATE_TIME STRING,
  EPARCHY_CODE STRING,
  PROVINCE_CODE STRING,
  opt STRING,
  opttime STRING,
  in_time STRING,
  datasource STRING,
  cdhtime STRING,
  database_tag STRING,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID,PRODUCT_ID,START_DATE) NOT ENFORCED
)
WITH (
  'bucket' = '256',
  'consumer.expiration-time' = '72h',
  'file.format' = 'avro',
  'log.scan.remove-normalize' = 'true',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90'
);

-- 过滤后的用户表
CREATE TEMPORARY VIEW filtered_user AS
SELECT USER_ID, SERIAL_NUMBER
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user`
WHERE REMOVE_TAG IN ('0','1','3')
  AND NET_TYPE_CODE IN ('33','50');

-- 过滤后的产品表
CREATE TEMPORARY VIEW filtered_user_product AS
SELECT *
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product`
WHERE CAST(TO_TIMESTAMP(END_DATE, 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) > CAST(TIMESTAMP '2020-05-28 22:11:08' AS TIMESTAMP_LTZ(3));

-- 只订购一款A包且生效
CREATE TEMPORARY VIEW only_one_a_user AS
SELECT USER_ID
FROM filtered_user_product
WHERE PRODUCT_ID IN (
  '90381980', '90382029', '90382030', '90382035', '90715165', '90723404',
  '90991656', '91277029', '91277048', '91277054', '90366811', '90366812', '90366813',
  '91008468', '91081955', '91081967', '91082147', '91082150', '91328961', '91328962',
  '91328963', '91328964', '91328967', '91328968', '91328970', '91334665', '91334676',
  '91334678', '91334680', '91334681', '91334684', '91334685', '91334687', '91334690',
  '91334691', '91334692', '90991668', '91233779', '91295467', '91295475', '91373798',
  '91258941', '91283113', '90612474', '91233780', '91295480', '91295482', '91319744'
)
AND CAST(TO_TIMESTAMP(END_DATE, 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) > CURRENT_TIMESTAMP
GROUP BY USER_ID
HAVING COUNT(DISTINCT PRODUCT_ID) = 1;

-- 没有订购除A以外的多日包
CREATE TEMPORARY VIEW no_other_package_user AS
SELECT USER_ID
FROM filtered_user_product
WHERE PRODUCT_ID NOT IN (
  '90381980', '90382029', '90382030', '90382035', '90715165', '90723404',
  '90991656', '91277029', '91277048', '91277054', '90366811', '90366812', '90366813',
  '91008468', '91081955', '91081967', '91082147', '91082150', '91328961', '91328962',
  '91328963', '91328964', '91328967', '91328968', '91328970', '91334665', '91334676',
  '91334678', '91334680', '91334681', '91334684', '91334685', '91334687', '91334690',
  '91334691', '91334692', '90991668', '91233779', '91295467', '91295475', '91373798',
  '91258941', '91283113', '90612474', '91233780', '91295480', '91295482', '91319744'
)
AND CAST(TO_TIMESTAMP(END_DATE, 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) > CURRENT_TIMESTAMP
GROUP BY USER_ID;

-- 最终user_id
CREATE TEMPORARY VIEW final_user AS
SELECT a.USER_ID
FROM only_one_a_user a
LEFT JOIN no_other_package_user n ON a.USER_ID = n.USER_ID
WHERE n.USER_ID IS NULL;

-- 最终插入
INSERT INTO `ubd_sscj_prod_flink`.`dwa_r_paimon_HeFei_product_info`
SELECT
  p.USER_ID,
  f.SERIAL_NUMBER,
  p.PROVINCE_CODE,
  p.PRODUCT_ID,
  p.START_DATE,
  p.END_DATE,
  CURRENT_TIMESTAMP AS PAIMON_TIME
FROM filtered_user_product p
JOIN final_user u ON p.USER_ID = u.USER_ID
JOIN filtered_user f ON p.USER_ID = f.USER_ID
WHERE p.PRODUCT_ID IN (
  '90381980', '90382029', '90382030', '90382035', '90715165', '90723404',
  '90991656', '91277029', '91277048', '91277054', '90366811', '90366812', '90366813',
  '91008468', '91081955', '91081967', '91082147', '91082150', '91328961', '91328962',
  '91328963', '91328964', '91328967', '91328968', '91328970', '91334665', '91334676',
  '91334678', '91334680', '91334681', '91334684', '91334685', '91334687', '91334690',
  '91334691', '91334692', '90991668', '91233779', '91295467', '91295475', '91373798',
  '91258941', '91283113', '90612474', '91233780', '91295480', '91295482', '91319744'
)
AND CAST(TO_TIMESTAMP(p.END_DATE, 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) > CURRENT_TIMESTAMP;