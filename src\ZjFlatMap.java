package com.unicom.realtime.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.unicom.realtime.entity.ZjData;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ZjFlatMap extends RichFlatMapFunction<ConsumerRecord<String, String>, ZjData> {

    private final static Logger logger = LoggerFactory.getLogger(ZjFlatMap.class);
    private Gson gson;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        gson = new Gson();
    }

    @Override
    public void flatMap(ConsumerRecord<String, String> record, Collector<ZjData> collector) throws Exception {
        logger.info("===record===" + record.value());
        JSONObject jsonObject = JSON.parseObject(record.value());

//        Object productIn = jsonObject.get("PRODUCT_IN");
//        logger.info("===productIn===" + JSON.toJSONString(productIn));
//        if (productIn != null) {
//            ZjData zjData = new ZjData();
//            zjData.setSerialNumber(String.valueOf(jsonObject.get("serial_number")));
//            zjData.setIsContractUser(String.valueOf(jsonObject.get("IS_CONTRACT_USER")));
//            zjData.setMainProductMonthlyFee(String.valueOf(jsonObject.get("MAIN_PRODUCT_MONTHLY_FEE")));
//            zjData.setProductIn(String.valueOf(jsonObject.get("PRODUCT_IN")));
//            zjData.setProductIs2i2c(String.valueOf(jsonObject.get("PRODUCT_IS_2I2C")));
//            zjData.setRProductIn(String.valueOf(jsonObject.get("R_PRODUCT_IN")));
//            zjData.setRProductIs2i2c(String.valueOf(jsonObject.get("R_PRODUCT_IS_2I2C")));
//            zjData.setValidMainProduct(String.valueOf(jsonObject.get("VALID_MAIN_PRODUCT")));
//            logger.info("===ZjData===" + zjData.toString());
//
//            collector.collect(zjData);
//        }

        //云盘标签
        Object level = jsonObject.get("level");
        if (level != null) {
            ZjData zjData = new ZjData();
            zjData.setSerialNumber(String.valueOf(jsonObject.get("phone")));
            zjData.setLevel(String.valueOf(jsonObject.get("level")));
            logger.info("===ZjData===" + zjData.toString());
            collector.collect(zjData);
        }
        Object pickUpCount = jsonObject.get("pickUpCount");
        if (pickUpCount != null) {
            ZjData zjData = new ZjData();
            zjData.setSerialNumber(String.valueOf(jsonObject.get("phone")));
            zjData.setInterceptCount(String.valueOf(jsonObject.get("interceptCount")));
            zjData.setPickUpCount(String.valueOf(jsonObject.get("pickUpCount")));
            zjData.setOrderStatus(String.valueOf(jsonObject.get("orderStatus")));
            zjData.setExpireRemainingDays(String.valueOf(jsonObject.get("expireRemainingDays")));
            zjData.setCanBindMemberCount(String.valueOf(jsonObject.get("canBindMemberCount")));
            zjData.setAlreadyBindMemberCount(String.valueOf(jsonObject.get("alreadyBindMemberCount")));
            zjData.setBindDevice(String.valueOf(jsonObject.get("bindDevice")));

            logger.info("===ZjData===" + zjData.toString());
            collector.collect(zjData);
        }

    }

}
