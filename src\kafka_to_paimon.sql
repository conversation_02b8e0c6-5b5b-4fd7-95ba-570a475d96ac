SET
  'execution.runtime-mode' = 'streaming';
SET 'execution.checkpointing.interval' = '60s';
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;

-- Kafka Source Table
CREATE TABLE IF NOT EXISTS kafka_source_table (
  `NUM` STRING,
  `PROVINCE_CODE` STRING,
  `CHANNEL_ID` STRING,
  `OPERATOR_ID` STRING,
  `CERT_TYPE` STRING,
  `CERT_NAME` STRING,
  `CERT_NUM` STRING,
  `SYS_CODE` STRING,
  `COMPARE_TIME` STRING,
  `RESP_CODE` STRING,
  `RESP_DESC` STRING,
  `LOAD_TIME` STRING
) WITH (
  'connector' = 'kafka',
  'topic' = 'USER_DATA_TOPIC', -- 请替换为您的 Topic
  'properties.group.id' = 'PAIMON_WRITER_GROUP', -- 请替换为您的 Group ID
  'scan.startup.mode' = 'latest-offset',
  'format' = 'csv',
  'csv.field-delimiter' = U&'\0001',
  'properties.bootstrap.servers' = '10.172.68.13:32005,10.172.68.15:32002,10.172.68.14:32002,10.172.68.12:32005,10.172.68.11:32005,10.172.68.20:32002,10.172.68.19:32002,10.172.68.18:32002,10.172.68.17:32002,10.172.68.16:32002',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="lzprod" password="KLIDz#Gi8ed!I3zb";'
);

-- Paimon Sink Table
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_kafka_data_to_paimon` (
  `NUM` STRING,
  `PROVINCE_CODE` STRING,
  `CHANNEL_ID` STRING,
  `OPERATOR_ID` STRING,
  `CERT_TYPE` STRING,
  `CERT_NAME` STRING,
  `CERT_NUM` STRING,
  `SYS_CODE` STRING,
  `COMPARE_TIME` STRING,
  `RESP_CODE` STRING,
  `RESP_DESC` STRING,
  `LOAD_TIME` STRING,
  CONSTRAINT `PK_NUM` PRIMARY KEY (`NUM`) NOT ENFORCED
) WITH (
  'bucket' = '8',
  'bucket-key' = 'NUM',
  'file.format' = 'avro'
);

-- Insert data from Kafka to Paimon
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_kafka_data_to_paimon`
SELECT
  `NUM`,
  `PROVINCE_CODE`,
  `CHANNEL_ID`,
  `OPERATOR_ID`,
  `CERT_TYPE`,
  `CERT_NAME`,
