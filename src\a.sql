SET
  'execution.runtime-mode' = 'streaming';
SET 'execution.checkpointing.interval' = '60s';
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` (
  phone STRING PRIMARY KEY NOT ENFORCED,
  interceptCount STRING,
  pickUpCount STRING,
  orderStatus STRING,
  expireRemainingDays STRING,
  canBindMemberCount STRING,
  alreadyBindMemberCount STRING,
  bindDevice STRING,
  msgType STRING,
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING, BYTES >
) WITH (
  'connector' = 'paimon',
  'consumer.expiration-time' = '72h',
  'path' = 'hdfs:///paimon/ubd_sscj_prod_flink/ods_r_paimon_zaixian_anquangj',
  'auto-create' = 'true',
  'merge-engine' = 'partial-update',
  'sequence.field' = 'paimon_time',
  'partial-update.ignore-delete' = 'true',
  'changelog-producer' = 'input',
  'fields.interceptCount.default-value' = 'null',
  'fields.pickUpCount.default-value' = 'null',
  'fields.orderStatus.default-value' = 'null',
  'fields.expireRemainingDays.default-value' = 'null',
  'fields.canBindMemberCount.default-value' = 'null',
  'fields.alreadyBindMemberCount.default-value' = 'null',
  'fields.bindDevice.default-value' = 'null'
);

CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_zaixian_anquangj_info_new` (
  phone STRING PRIMARY KEY NOT ENFORCED,
  interceptCount STRING,
  pickUpCount STRING,
  orderStatus STRING,
  expireRemainingDays STRING,
  canBindMemberCount STRING,
  alreadyBindMemberCount STRING,
  bindDevice STRING
) WITH (
  'connector' = 'upsert-kafka',
  'topic' = 'IN_KAFKA_TO_HBASE',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092',
  'key.format' = 'json',
  'value.format' = 'json',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";'
);

-- 创建临时视图获取最新的完整记录
CREATE TEMPORARY VIEW latest_data AS
SELECT
  t1.*
FROM (
  SELECT
    phone,
    interceptCount,
    pickUpCount,
    orderStatus,
    expireRemainingDays,
    canBindMemberCount,
    alreadyBindMemberCount,
    bindDevice,
    paimon_time,
    ROW_NUMBER() OVER (PARTITION BY phone ORDER BY paimon_time DESC) AS rn
  FROM
    `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj`
    /*+ OPTIONS('scan.mode'='compacted-full', 'consumer-id' = 'rtag_2025-04-24-4') */
) t1
WHERE
  t1.rn = 1;

-- 将最新数据放入Kafka
INSERT INTO
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_zaixian_anquangj_info_new`
SELECT
  phone,
  CASE WHEN interceptCount IS NULL THEN (
    SELECT interceptCount FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND interceptCount IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE interceptCount END AS interceptCount,
  CASE WHEN pickUpCount IS NULL THEN (
    SELECT pickUpCount FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND pickUpCount IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE pickUpCount END AS pickUpCount,
  CASE WHEN orderStatus IS NULL THEN (
    SELECT orderStatus FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND orderStatus IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE orderStatus END AS orderStatus,
  CASE WHEN expireRemainingDays IS NULL THEN (
    SELECT expireRemainingDays FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND expireRemainingDays IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE expireRemainingDays END AS expireRemainingDays,
  CASE WHEN canBindMemberCount IS NULL THEN (
    SELECT canBindMemberCount FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND canBindMemberCount IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE canBindMemberCount END AS canBindMemberCount,
  CASE WHEN alreadyBindMemberCount IS NULL THEN (
    SELECT alreadyBindMemberCount FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND alreadyBindMemberCount IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE alreadyBindMemberCount END AS alreadyBindMemberCount,
  CASE WHEN bindDevice IS NULL THEN (
    SELECT bindDevice FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_anquangj` 
    WHERE phone = l.phone AND bindDevice IS NOT NULL 
    ORDER BY paimon_time DESC LIMIT 1
  ) ELSE bindDevice END AS bindDevice
FROM 
  latest_data l;