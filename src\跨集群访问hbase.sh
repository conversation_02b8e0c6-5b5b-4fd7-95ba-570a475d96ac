#!/bin/bash

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项] <rowkey> <列簇>"
    echo "   或: $0 [选项] --file <rowkeys-file> <列簇>"
    echo
    echo "必选参数:"
    echo "  <rowkey>              要查询的rowkey (或使用--file选项指定包含多个rowkey的文件)"
    echo "  <列簇>                要查询的列簇"
    echo
    echo "选项:"
    echo "  -z, --zookeeper       ZooKeeper地址，格式为host:port[,host:port,...]"
    echo "  -t, --table           HBase表名"
    echo "  -u, --user            访问HBase的用户名"
    echo "  -h, --hbase-home      HBase客户端安装目录"
    echo "  -c, --column          指定列名(可选，默认查询整个列簇)"
    echo "  -r, --znode-root      HBase在ZooKeeper中的根路径(默认为/hbase)"
    echo "  -d, --debug           启用调试模式"
    echo "  -p, --port            ZooKeeper端口(默认2181)"
    echo "  --test-connection     仅测试连接，不执行查询"
    echo "  --reverse-rowkey      使用倒置的rowkey进行查询"
    echo "  --file                指定包含多个rowkey的文件，每行一个rowkey"
    echo "  --help                显示此帮助信息并退出"
    echo
    echo "示例:"
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" -u \"hbase\" -h \"/usr/hdp/current/hbase-client\" \"row1\" \"cf\""
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" -r \"/hbase-secure\" \"row1\" \"cf\""
    echo "  $0 --test-connection -z \"zk1,zk2,zk3\" -r \"/hbase-secure\""
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" --reverse-rowkey \"row1\" \"cf\""
    echo "  $0 -z \"zk1,zk2,zk3\" -t \"namespace:table\" --file \"rowkeys.txt\" \"cf\""
    exit 1
}

# 默认值
HBASE_HOME="/usr/hdp/current/hbase-client"
HBASE_USER="hbase"
COLUMN=""
ZNODE_ROOT="/hbase"
DEBUG=false
ZK_PORT="2181"
TEST_CONNECTION=false
REVERSE_ROWKEY=false
ROWKEY_FILE=""
ROWKEYS=()

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        -z|--zookeeper)
            ZOOKEEPER_HOSTS="$2"
            shift 2
            ;;
        -t|--table)
            TABLE_NAME="$2"
            shift 2
            ;;
        -u|--user)
            HBASE_USER="$2"
            shift 2
            ;;
        -h|--hbase-home)
            HBASE_HOME="$2"
            shift 2
            ;;
        -c|--column)
            COLUMN="$2"
            shift 2
            ;;
        -r|--znode-root)
            ZNODE_ROOT="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG=true
            shift
            ;;
        -p|--port)
            ZK_PORT="$2"
            shift 2
            ;;
        --test-connection)
            TEST_CONNECTION=true
            shift
            ;;
        --reverse-rowkey)
            REVERSE_ROWKEY=true
            shift
            ;;
        --file)
            ROWKEY_FILE="$2"
            shift 2
            ;;
        --help)
            show_help
            ;;
        -*)
            echo "未知选项: $1"
            show_help
            ;;
        *)
            # 处理位置参数
            if [ -z "$ROWKEY" ] && [ -z "$ROWKEY_FILE" ]; then
                # 非文件模式下，第一个位置参数是rowkey
                ROWKEY="$1"
            elif [ -z "$COLUMN_FAMILY" ]; then
                # 最后一个位置参数总是列簇
                COLUMN_FAMILY="$1"
            else
                echo "错误: 多余的参数 '$1'"
                show_help
            fi
            shift
            ;;
    esac
done

# 格式化ZooKeeper地址
format_zk_quorum() {
    local hosts=$1
    local port=$2
    local result=""
    
    # 分割主机名并添加端口
    IFS=',' read -ra HOST_ARRAY <<< "$hosts"
    for i in "${HOST_ARRAY[@]}"; do
        # 检查主机名是否已包含端口
        if [[ $i == *":"* ]]; then
            result="${result}${i},"
        else
            result="${result}${i}:${port},"
        fi
    done
    
    # 移除最后的逗号
    result=${result%,}
    echo $result
}

# 检查ZooKeeper连接
check_zk_connection() {
    local zk_quorum=$1
    local znode=$2
    
    if [ -x "$(command -v zkCli.sh)" ]; then
        echo "使用zkCli.sh检查ZooKeeper连接..."
        zkCli.sh -server $zk_quorum ls $znode
        return $?
    elif [ -x "$(command -v zookeeper-client)" ]; then
        echo "使用zookeeper-client检查ZooKeeper连接..."
        zookeeper-client -server $zk_quorum ls $znode
        return $?
    elif [ -x "$HBASE_HOME/bin/hbase" ]; then
        echo "使用HBase自带工具检查ZooKeeper连接..."
        $HBASE_HOME/bin/hbase zkcli -server $zk_quorum ls $znode
        return $?
    else
        echo "警告: 未找到ZooKeeper客户端工具，无法测试连接"
        return 1
    fi
}

# 检查必要参数
if [ -z "$ZOOKEEPER_HOSTS" ]; then
    echo "错误: 必须指定ZooKeeper地址 (-z 选项)"
    show_help
fi

# 格式化ZooKeeper地址
ZOOKEEPER_QUORUM=$(format_zk_quorum "$ZOOKEEPER_HOSTS" "$ZK_PORT")

# 如果只是测试连接
if [ "$TEST_CONNECTION" = true ]; then
    echo "测试与ZooKeeper的连接..."
    echo "ZooKeeper地址: $ZOOKEEPER_QUORUM"
    echo "ZNode根路径: $ZNODE_ROOT"
    check_zk_connection "$ZOOKEEPER_QUORUM" "$ZNODE_ROOT"
    exit $?
fi

# 检查是否提供了rowkey文件
if [ ! -z "$ROWKEY_FILE" ]; then
    # 检查文件是否存在
    if [ ! -f "$ROWKEY_FILE" ]; then
        echo "错误: 指定的rowkey文件不存在: $ROWKEY_FILE"
        exit 1
    fi
    
    # 读取文件中的rowkey
    while IFS= read -r line || [ -n "$line" ]; do
        # 跳过空行和注释行
        if [ ! -z "$line" ] && [[ ! "$line" =~ ^# ]]; then
            ROWKEYS+=("$line")
        fi
    done < "$ROWKEY_FILE"
    
    if [ ${#ROWKEYS[@]} -eq 0 ]; then
        echo "错误: 文件中没有有效的rowkey: $ROWKEY_FILE"
        exit 1
    fi
    
    if [ "$DEBUG" = true ]; then
        echo "从文件 $ROWKEY_FILE 中读取了 ${#ROWKEYS[@]} 个rowkey"
    fi
else
    # 使用命令行参数中的rowkey
    if [ -z "$ROWKEY" ]; then
        echo "错误: 必须提供rowkey或使用--file选项指定rowkey文件"
        show_help
    fi
    ROWKEYS=("$ROWKEY")
fi

# 检查其他必要参数
if [ -z "$COLUMN_FAMILY" ]; then
    echo "错误: 必须提供列簇"
    show_help
fi

if [ -z "$TABLE_NAME" ]; then
    echo "错误: 必须指定HBase表名 (-t 选项)"
    show_help
fi

# 检查HBase客户端是否存在
if [ ! -d "$HBASE_HOME" ]; then
    echo "错误: HBase客户端目录不存在: $HBASE_HOME"
    exit 1
fi

# 创建临时配置文件
TEMP_CONF_DIR=$(mktemp -d)
echo "创建临时配置目录: $TEMP_CONF_DIR"

cat > $TEMP_CONF_DIR/hbase-site.xml << EOF
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>hbase.zookeeper.quorum</name>
        <value>${ZOOKEEPER_QUORUM}</value>
    </property>
    <property>
        <name>zookeeper.znode.parent</name>
        <value>${ZNODE_ROOT}</value>
    </property>
    <property>
        <name>hbase.client.keyvalue.maxsize</name>
        <value>10485760</value>
    </property>
    <property>
        <name>hbase.rpc.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.operation.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.scanner.timeout.period</name>
        <value>120000</value>
    </property>
    <property>
        <name>zookeeper.session.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.retries.number</name>
        <value>10</value>
    </property>
</configuration>
EOF

# 设置环境变量
export HBASE_HOME
export PATH=$HBASE_HOME/bin:$PATH
export HADOOP_USER_NAME=${HBASE_USER}
export HBASE_CONF_DIR=$TEMP_CONF_DIR

if [ "$DEBUG" = true ]; then
    echo "调试信息:"
    echo "  HBASE_HOME = $HBASE_HOME"
    echo "  HBASE_CONF_DIR = $HBASE_CONF_DIR"
    echo "  HADOOP_USER_NAME = $HADOOP_USER_NAME"
    echo "  配置文件内容:"
    cat $TEMP_CONF_DIR/hbase-site.xml
    
    # 尝试列出表
    echo "尝试列出HBase表:"
    $HBASE_HOME/bin/hbase shell << EOF
list
EOF
fi

# 执行HBase查询
echo "-----------------------------------------------------"
echo "正在查询HBase..."
echo "ZooKeeper: $ZOOKEEPER_QUORUM"
echo "ZNode根路径: $ZNODE_ROOT"
echo "表名: $TABLE_NAME"
echo "要查询的rowkey数量: ${#ROWKEYS[@]}"
echo "列簇: $COLUMN_FAMILY"
if [ ! -z "$COLUMN" ]; then
    echo "指定列: $COLUMN"
fi
echo "-----------------------------------------------------"

# 准备HBase Shell脚本
HBASE_SCRIPT=$(mktemp)
echo "# HBase查询脚本" > $HBASE_SCRIPT

# 为每个rowkey生成查询命令
for rk in "${ROWKEYS[@]}"; do
    QUERY_ROWKEY="$rk"
    
    # 如果启用了rowkey反转，则执行反转
    if [ "$REVERSE_ROWKEY" = true ]; then
        ORIGINAL_ROWKEY="$QUERY_ROWKEY"
        # 使用rev命令反转rowkey
        QUERY_ROWKEY=$(echo "$QUERY_ROWKEY" | rev)
        echo "# 查询rowkey: $QUERY_ROWKEY (原始值: $ORIGINAL_ROWKEY 的反转)" >> $HBASE_SCRIPT
    else
        echo "# 查询rowkey: $QUERY_ROWKEY" >> $HBASE_SCRIPT
    fi
    
    # 构建查询命令
    if [ -z "$COLUMN" ]; then
        echo "get '${TABLE_NAME}', '${QUERY_ROWKEY}', '${COLUMN_FAMILY}'" >> $HBASE_SCRIPT
    else
        echo "get '${TABLE_NAME}', '${QUERY_ROWKEY}', '${COLUMN}'" >> $HBASE_SCRIPT
    fi
    
    # 添加分隔符
    echo "puts \"-----------------------------------------------------\"" >> $HBASE_SCRIPT
done

if [ "$DEBUG" = true ]; then
    echo "HBase查询脚本内容:"
    cat $HBASE_SCRIPT
fi

# 使用hbase shell执行查询
$HBASE_HOME/bin/hbase shell $HBASE_SCRIPT

# 检查执行结果
RESULT=$?
if [ $RESULT -eq 0 ]; then
    echo "查询完成，返回状态码: $RESULT"
    echo "已查询 ${#ROWKEYS[@]} 个rowkey"
else
    echo "查询失败，返回状态码: $RESULT"
    echo "可能的问题:"
    echo "1. ZooKeeper地址不正确"
    echo "2. HBase的ZNode根路径不是 $ZNODE_ROOT (使用-r选项指定正确的路径)"
    echo "3. 用户 $HBASE_USER 没有访问权限"
    echo "4. 表 $TABLE_NAME 不存在"
    echo
    echo "建议:"
    echo "1. 使用--test-connection选项测试ZooKeeper连接"
    echo "2. 使用-d选项启用调试模式获取更多信息"
    echo "3. 联系集群管理员确认正确的ZooKeeper地址和ZNode根路径"
fi

# 清理临时文件
echo "清理临时配置目录: $TEMP_CONF_DIR"
rm -rf $TEMP_CONF_DIR
rm -f $HBASE_SCRIPT

exit $RESULT 