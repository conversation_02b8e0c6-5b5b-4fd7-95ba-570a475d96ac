#!/bin/bash

# 检查参数数量
if [ $# -ne 2 ]; then
    echo "用法: $0 <rowkey> <列簇>"
    echo "示例: $0 'rowkey123' 'cf'"
    exit 1
fi

# 设置参数
ROWKEY=$1
COLUMN_FAMILY=$2

# HBase配置
HBASE_HOME="/usr/hdp/current/hbase-client"
ZOOKEEPER_QUORUM="10.177.17.13:2181,10.177.17.17:2181,10.177.17.24:2181"
HBASE_USER="hbase"
TABLE_NAME="ctg810544697531_hh_fed_sub23_lclabel:label_lc_realtime_data"

# 检查HBase客户端是否存在
if [ ! -d "$HBASE_HOME" ]; then
    echo "错误: HBase客户端目录不存在: $HBASE_HOME"
    exit 1
fi

# 设置环境变量
export HBASE_HOME
export PATH=$HBASE_HOME/bin:$PATH
export HADOOP_USER_NAME=hbase

# 执行HBase查询
echo "正在查询HBase..."
echo "RowKey: $ROWKEY"
echo "列簇: $COLUMN_FAMILY"

# 使用hbase shell执行查询
$HBASE_HOME/bin/hbase shell << EOF
# 获取数据
get '${TABLE_NAME}', '${ROWKEY}', '${COLUMN_FAMILY}'
EOF

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "查询完成"
else
    echo "查询失败"
    exit 1
fi
