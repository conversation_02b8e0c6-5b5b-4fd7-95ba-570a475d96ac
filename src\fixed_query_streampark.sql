-- StreamPark上执行的FlinkSQL
-- 设置执行参数
SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'ctg782612549842_hh_fed_sub15_sssc_prod15';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

-- 只用一个Catalog简化操作
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

-- 创建或替换临时视图（不使用CREATE TEMPORARY VIEW语法）
CREATE TEMPORARY TABLE specified_products (
    product_id STRING
) WITH (
    'connector' = 'values',
    'data-id' = 'specified_products'
);

INSERT INTO specified_products VALUES 
('90381980'), ('90382029'), ('90382030'), ('90382035'), ('90715165'), ('90723404'), 
('90991656'), ('91277029'), ('91277048'), ('91277054'), ('90366811'), ('90366812'), 
('90366813'), ('91008468'), ('91081955'), ('91081967'), ('91082147'), ('91082150'), 
('91328961'), ('91328962'), ('91328963'), ('91328964'), ('91328967'), ('91328968'), 
('91328970'), ('91334665'), ('91334676'), ('91334678'), ('91334680'), ('91334681'), 
('91334684'), ('91334685'), ('91334687'), ('91334690'), ('91334691'), ('91334692'), 
('90991668'), ('91233779'), ('91295467'), ('91295475'), ('91373798'), ('91258941'), 
('91283113'), ('90612474'), ('91233780'), ('91295480'), ('91295482'), ('91319744');

-- 主查询：使用简化的SQL语法
INSERT INTO `ubd_sscj_prod_flink`.`dwa_r_paimon_HeFei_product_info`
SELECT 
    u.USER_ID,
    u.PRODUCT_ID,
    u.START_DATE,
    u.END_DATE,
    CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time
FROM `ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` u
WHERE 
    -- 这条记录是指定产品
    EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = u.PRODUCT_ID)
    -- 这条记录是活跃的（未过期）
    AND CAST(u.END_DATE AS BIGINT) > CAST(DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyyMMdd') AS BIGINT)
    -- 产品明天到期
    AND TO_DATE(FROM_UNIXTIME(UNIX_TIMESTAMP(u.END_DATE, 'yyyyMMdd'))) = 
       TO_DATE(DATE_FORMAT(TIMESTAMPADD(DAY, 1, CURRENT_TIMESTAMP), 'yyyy-MM-dd'))
    -- 用户只有一个指定产品且是生效的
    AND 1 = (
        SELECT COUNT(1) 
        FROM `ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` u2
        WHERE u2.USER_ID = u.USER_ID
          AND EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = u2.PRODUCT_ID)
          AND CAST(u2.END_DATE AS BIGINT) > CAST(DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyyMMdd') AS BIGINT)
    ); 