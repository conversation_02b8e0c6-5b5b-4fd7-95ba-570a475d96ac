CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;

SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'hh_slfn2_sschj';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';


CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_cmw_cust_manager_ref` (
  id STRING,
  user_id STRING,
  customer_mobile STRING,
  customer_mobile_fnv STRING,
  is_vip STRING,
  manager_id STRING,
  is_main STRING,
  is_recover STRING,
  create_id STRING,
  create_time STRING,
  update_id STRING,
  update_time STRING,
  opt STRING,
  opttime STRING,
  in_time STRING,
  datasource STRING,
  cdhtime STRING,
  database_tag STRING,
  kafka_in_time TIMESTAMP(3),
  kafka_out_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  headers MAP<STRING,BYTES> METADATA FROM 'headers',
  CONSTRAINT `PK_id` PRIMARY KEY (id) NOT ENFORCED
) WITH (
  'bucket' = '32',
  'bucket-key' = 'id',
  'num-sorted-run.stop-trigger' = '2147483647',
  'snapshot.expire.execution-mode' = 'async',
  'log.scan.remove-normalize' = 'true',
  'snapshot.time-retained' = '12h',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h',
  'sort-spill-threshold' = '10'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` (
  PARTITION_ID STRING,
  USER_ID STRING,
  CUST_ID STRING,
  USECUST_ID STRING,
  BRAND_CODE STRING,
  PRODUCT_ID STRING,
  EPARCHY_CODE STRING,
  CITY_CODE STRING,
  USER_PASSWD STRING,
  USER_DIFF_CODE STRING,
  USER_TYPE_CODE STRING,
  SERIAL_NUMBER STRING,
  NET_TYPE_CODE STRING,
  SCORE_VALUE STRING,
  CREDIT_CLASS STRING,
  BASIC_CREDIT_VALUE STRING,
  CREDIT_VALUE STRING,
  ACCT_TAG STRING,
  PREPAY_TAG STRING,
  IN_DATE STRING,
  OPEN_DATE STRING,
  OPEN_MODE STRING,
  OPEN_DEPART_ID STRING,
  OPEN_STAFF_ID STRING,
  IN_DEPART_ID STRING,
  IN_STAFF_ID STRING,
  REMOVE_TAG STRING,
  DESTROY_TIME STRING,
  REMOVE_EPARCHY_CODE STRING,
  REMOVE_CITY_CODE STRING,
  REMOVE_DEPART_ID STRING,
  REMOVE_REASON_CODE STRING,
  PRE_DESTROY_TIME STRING,
  FIRST_CALL_TIME STRING,
  LAST_STOP_TIME STRING,
  USER_STATE_CODESET STRING,
  MPUTE_MONTH_FEE STRING,
  MPUTE_DATE STRING,
  UPDATE_TIME STRING,
  ASSURE_CUST_ID STRING,
  ASSURE_TYPE_CODE STRING,
  ASSURE_DATE STRING,
  DEVELOP_STAFF_ID STRING,
  DEVELOP_DATE STRING,
  DEVELOP_EPARCHY_CODE STRING,
  DEVELOP_CITY_CODE STRING,
  DEVELOP_DEPART_ID STRING,
  DEVELOP_NO STRING,
  REMARK STRING,
  CREDIT_RULE_ID STRING,
  CONTRACT_ID STRING,
  CHANGEUSER_DATE STRING,
  IN_NET_MODE STRING,
  PRODUCT_TYPE_CODE STRING,
  MAIN_DISCNT_CODE STRING,
  PRODUCT_SPEC STRING,
  PROVINCE_CODE STRING,
  OPT STRING,
  OPTTIME STRING,
  CDHTIME STRING,
  DATASOURCE STRING,
  IN_TIME STRING,
  DATABASE_TAG STRING,
  EVENT_TIME TIMESTAMP(3) NOT NULL COMMENT '事件时间戳',
  KAFKA_TIME TIMESTAMP(3) NOT NULL COMMENT 'kafka时间戳',
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  HEADERS MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
  'bucket' = '256', 
  'bucket-key' = 'USER_ID',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'write-only' = 'true',
  'snapshot.time-retained' = '1h'
);


-- 创建Kafka目标表
CREATE TABLE IF NOT EXISTS hive_catalog.ubd_sscj_prod_flink.dwa_r_kafka_bbgx_011 (
    `id` STRING,
    `user_id` STRING,
    `manager_id` STRING,
    `is_vip` STRING,
    `is_main` STRING,
    `is_recover` STRING,
    `customer_mobile_fnv` STRING,
    `customer_mobile` STRING,
    `update_id` STRING,
    `update_time` STRING,
    `province_code` STRING,
    `create_id` STRING,
    `create_time` STRING
)
WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'CMW_CUST_MANAGER',
  'key.format' = 'json',
  'key.fields' = 'user_id',
  'value.format' = 'json',
  'key.json.ignore-parse-errors' = 'true',
  'key.json.fail-on-missing-field' = 'false',
  'value.json.ignore-parse-errors' = 'true',
  'value.json.fail-on-missing-field' = 'false'
);

-- 核心处理逻辑
INSERT INTO hive_catalog.ubd_sscj_prod_flink.dwa_r_kafka_bbgx_011
SELECT
    ref.id,
    ref.user_id,
    ref.manager_id,
    ref.is_vip,
    ref.is_main,
    ref.is_recover,
    ref.customer_mobile_fnv,
    ref.customer_mobile,
    ref.update_id,
    ref.update_time,
    usr.PROVINCE_CODE AS province_code,
    ref.create_id,
    ref.create_time
FROM
    (SELECT * FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_cmw_cust_manager_ref` /*+ OPTIONS('scan.mode' = 'latest', 'consumer-id' = 'bbgx_r_tag_20250804') */) AS ref
INNER JOIN
    paimon_catalog.ubd_sscj_prod_flink.ods_r_paimon_tf_f_user AS usr
ON
    ref.user_id = usr.USER_ID
WHERE
    usr.PROVINCE_CODE IN ('11', '011');
