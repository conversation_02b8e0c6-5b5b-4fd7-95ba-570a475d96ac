-- 用户数据迁移和更新脚本 - 分步执行版本
-- 建议按步骤逐一执行，确保每一步都成功完成后再执行下一步

-- ========================================
-- 步骤1：环境准备和表创建
-- ========================================

-- 使用Paimon Catalog
USE CATALOG paimon_catalog;

-- 创建新的Paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID STRING NOT NULL,
    IN_DATE STRING,
    OPEN_DATE STRING,
    DESTROY_TIME STRING,
    paimon_ingest_time TIMESTAMP(3) COMMENT 'Paimon 记录写入处理时间戳',
    PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/dwd_r_paimon_user_info_temp',
    'auto-create' = 'true',
    'merge-engine' = 'partial-update',
    'sequence.field' = 'paimon_ingest_time',
    'partial-update.ignore-delete' = 'true',
    'changelog-producer' = 'input',
    'bucket' = '64',
    'snapshot.time-retained' = '6h',
    'file.format' = 'avro',
    'metadata.stats-mode' = 'none',
    'consumer.expiration-time' = '72h'
);

-- 验证表创建成功
DESCRIBE `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- ========================================
-- 步骤2：从ods_r_paimon_tf_f_user表导入符合条件的USER_ID
-- ========================================

-- 首先查看符合条件的数据量
SELECT 
    COUNT(*) AS total_qualified_users,
    COUNT(DISTINCT USER_ID) AS distinct_user_ids
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user`
WHERE LENGTH(OPEN_DATE) = 12
  AND USER_ID IS NOT NULL;

-- 插入符合条件的USER_ID到新表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID,
    paimon_ingest_time
)
SELECT 
    USER_ID,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_ingest_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user`
WHERE LENGTH(OPEN_DATE) = 12
  AND USER_ID IS NOT NULL;

-- 验证插入结果
SELECT COUNT(*) AS inserted_user_count 
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- ========================================
-- 步骤3：从dwa_r_paimon_human_user_wide表获取详细信息并更新
-- ========================================

-- 首先查看匹配的数据量
SELECT 
    COUNT(*) AS matching_records
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_human_user_wide` t2 
    ON t1.USER_ID = t2.USER_ID;

-- 更新新表中的字段信息
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` (
    USER_ID,
    IN_DATE,
    OPEN_DATE,
    DESTROY_TIME
)
SELECT 
    t1.USER_ID,
    t2.IN_DATE,
    t2.OPEN_DATE,
    t2.DESTROY_TIME
    FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_human_user_wide` t2 
    ON t1.USER_ID = t2.USER_ID  where t1.USER_ID='1015012228752017';

-- 验证更新结果
SELECT 
    COUNT(*) AS total_records,
    COUNT(CASE WHEN IN_DATE IS NOT NULL THEN 1 END) AS records_with_in_date,
    COUNT(CASE WHEN OPEN_DATE IS NOT NULL THEN 1 END) AS records_with_open_date,
    COUNT(CASE WHEN DESTROY_TIME IS NOT NULL THEN 1 END) AS records_with_destroy_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- ========================================
-- 步骤4：将新表数据更新回ods_r_paimon_tf_f_user表
-- ========================================

-- 首先查看将要更新的记录数
SELECT 
    COUNT(*) AS records_to_update
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2 
    ON t1.USER_ID = t2.USER_ID;

-- 执行更新操作（将更新的数据插入到ods_r_paimon_tf_f_user表）
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` (
    PARTITION_ID, USER_ID, CUST_ID, USECUST_ID, BRAND_CODE, PRODUCT_ID, EPARCHY_CODE, CITY_CODE,
    USER_PASSWD, USER_DIFF_CODE, USER_TYPE_CODE, SERIAL_NUMBER, NET_TYPE_CODE, SCORE_VALUE,
    CREDIT_CLASS, BASIC_CREDIT_VALUE, CREDIT_VALUE, ACCT_TAG, PREPAY_TAG, IN_DATE, OPEN_DATE,
    OPEN_MODE, OPEN_DEPART_ID, OPEN_STAFF_ID, IN_DEPART_ID, IN_STAFF_ID, REMOVE_TAG, DESTROY_TIME,
    REMOVE_EPARCHY_CODE, REMOVE_CITY_CODE, REMOVE_DEPART_ID, REMOVE_REASON_CODE, MODIFY_TAG,
    UPDATE_TIME, UPDATE_DEPART_ID, UPDATE_STAFF_ID, RSRV_NUM1, RSRV_NUM2, RSRV_NUM3, RSRV_NUM4,
    RSRV_NUM5, RSRV_STR1, RSRV_STR2, RSRV_STR3, RSRV_STR4, RSRV_STR5, RSRV_STR6, RSRV_STR7,
    RSRV_STR8, RSRV_STR9, RSRV_STR10, RSRV_DATE1, RSRV_DATE2, RSRV_DATE3, RSRV_TAG1, RSRV_TAG2,
    RSRV_TAG3, PROVINCE_CODE, opt, opttime, in_time, datasource, cdhtime, database_tag,
    kafka_in_time, kafka_out_time, paimon_time, headers
)
SELECT 
    t1.PARTITION_ID, t1.USER_ID, t1.CUST_ID, t1.USECUST_ID, t1.BRAND_CODE, t1.PRODUCT_ID, 
    t1.EPARCHY_CODE, t1.CITY_CODE, t1.USER_PASSWD, t1.USER_DIFF_CODE, t1.USER_TYPE_CODE, 
    t1.SERIAL_NUMBER, t1.NET_TYPE_CODE, t1.SCORE_VALUE, t1.CREDIT_CLASS, t1.BASIC_CREDIT_VALUE, 
    t1.CREDIT_VALUE, t1.ACCT_TAG, t1.PREPAY_TAG, 
    COALESCE(t2.IN_DATE, t1.IN_DATE) AS IN_DATE,
    COALESCE(t2.OPEN_DATE, t1.OPEN_DATE) AS OPEN_DATE,
    t1.OPEN_MODE, t1.OPEN_DEPART_ID, t1.OPEN_STAFF_ID, t1.IN_DEPART_ID, t1.IN_STAFF_ID, 
    t1.REMOVE_TAG, 
    COALESCE(t2.DESTROY_TIME, t1.DESTROY_TIME) AS DESTROY_TIME,
    t1.REMOVE_EPARCHY_CODE, t1.REMOVE_CITY_CODE, t1.REMOVE_DEPART_ID, t1.REMOVE_REASON_CODE, 
    t1.MODIFY_TAG, t1.UPDATE_TIME, t1.UPDATE_DEPART_ID, t1.UPDATE_STAFF_ID, t1.RSRV_NUM1, 
    t1.RSRV_NUM2, t1.RSRV_NUM3, t1.RSRV_NUM4, t1.RSRV_NUM5, t1.RSRV_STR1, t1.RSRV_STR2, 
    t1.RSRV_STR3, t1.RSRV_STR4, t1.RSRV_STR5, t1.RSRV_STR6, t1.RSRV_STR7, t1.RSRV_STR8, 
    t1.RSRV_STR9, t1.RSRV_STR10, t1.RSRV_DATE1, t1.RSRV_DATE2, t1.RSRV_DATE3, t1.RSRV_TAG1, 
    t1.RSRV_TAG2, t1.RSRV_TAG3, t1.PROVINCE_CODE, t1.opt, t1.opttime, t1.in_time, t1.datasource, 
    t1.cdhtime, t1.database_tag, t1.kafka_in_time, t1.kafka_out_time, 
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time, t1.headers
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2 
    ON t1.USER_ID = t2.USER_ID;

-- ========================================
-- 步骤5：数据验证
-- ========================================

-- 验证最终结果
SELECT 
    'ods_r_paimon_tf_f_user updated records' AS description,
    COUNT(*) AS record_count
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` t1
INNER JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp` t2 
    ON t1.USER_ID = t2.USER_ID

UNION ALL

SELECT 
    'temp table total records' AS description,
    COUNT(*) AS record_count
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;

-- ========================================
-- 步骤6：清理（可选）
-- ========================================

-- 如果确认数据迁移成功，可以删除临时表
-- DROP TABLE IF EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwd_r_paimon_user_info_temp`;
