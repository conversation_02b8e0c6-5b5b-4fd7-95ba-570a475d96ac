#!/bin/bash
#var def

basedir="/data/disk01/hh_slfn2_sschj/private/liucz37/custAndUser3/"
ortherdir="/data/disk01/hh_slfn2_sschj/private/liucz37/custAndUser3/"
originaldir=$basedir"original/"
originalbakdir=$basedir"original_bak/"
shdir=$basedir"shell/"
toserverdir=$basedir"filestoserver/"
toserverdirover=$basedir"filestoserver_done/"

exec >>$shdir"puttosftp.log" 2>&1

#if [test -z "$1"]
#then
#    datestr=$(date +%Y%m%d)
#else
#    datestr=$1
#fi
mv ${ortherdir}filestoserver/*.* ${toserverdir}

datestr=$(date +%Y%m%d)

echo "mkdir $toserverdirover$datestr"
if [ -d "$toserverdirover$datestr" ];then
    echo "the dir is exit"
else
    mkdir -p $toserverdirover$datestr
fi
toserverdirover=$basedir"filestoserver_done/"$datestr

echo -e "\r\n\r\nprocess start "`date -R`


#echo "dedup serialNumber"
#${shdir}dedup.sh ${datestr}

echo "split file to 5600 files"
split -l 10000000 -d -a 4 ${toserverdir}${datestr}_xxxx_CustAndUser_all_A800_0000.txt ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_


split -l 10000000 -d -a 4 ${toserverdir}${datestr}_D800_CustAndUser_all_A800_0000.txt ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_

echo "delete original file"
rm ${toserverdir}${datestr}_xxxx_CustAndUser_all_A800_0000.txt

rm ${toserverdir}${datestr}_D800_CustAndUser_all_A800_0000.txt


echo "add .txt"
for f in ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_*
do
        mv $f $f".txt"
        filenum=${f#*_A800_}
        echo "now is "`date -R` "generate 5600 F100 files. filenum:$filenum"

        zip -pj ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt
        rm ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt
        
        # 调整上传顺序：先源文件后MD5
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip >>$shdir"puttosftp.log" 2>&1
        md5sum ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip | awk '{print $1}' > ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip.md5
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip.md5 >>$shdir"puttosftp.log" 2>&1
        sleep 1
        
        cp ${toserverdir}${datestr}_5600_CustAndUser_ALL_A800_${filenum}.txt.zip ${toserverdir}${datestr}_F100_CustAndUser_ALL_A800_${filenum}.txt.zip
        
        # F100文件同样调整顺序
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_F100_CustAndUser_ALL_A800_${filenum}.txt.zip >>$shdir"puttosftp.log" 2>&1
        md5sum ${toserverdir}${datestr}_F100_CustAndUser_ALL_A800_${filenum}.txt.zip | awk '{print $1}' > ${toserverdir}${datestr}_F100_CustAndUser_ALL_A800_${filenum}.txt.zip.md5
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_F100_CustAndUser_ALL_A800_${filenum}.txt.zip.md5 >>$shdir"puttosftp.log" 2>&1
        sleep 1
done

for f in ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_*
do
        mv $f $f".txt"
        filenum=${f#*_A800_}
        echo "now is "`date -R` "generate D800 files. filenum:$filenum"
        zip -pj ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt.zip ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt
        rm ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt
        
        # D800文件调整上传顺序
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt.zip >>$shdir"puttosftp.log" 2>&1
        md5sum ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt.zip | awk '{print $1}' > ${toserverdir}${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt.zip.md5
        ${shdir}sftp_to_server.sh ${basedir} ${datestr}_D800_CustAndUser_ALL_A800_${filenum}.txt.zip.md5 >>$shdir"puttosftp.log" 2>&1
done

echo "put files to sftp server current:"`date -R`

echo "backup 5600,f100,d800 files, move files to "$toserverdirover

...skipping one line

echo "process end"`date -R`

echo `date +"%Y-%m-%d_%H:%M"` "客户用户接口运行成功" >> /data/disk01/hh_slfn2_sschj/private/liucz37/custAndUser3/ziranren_custUser.log