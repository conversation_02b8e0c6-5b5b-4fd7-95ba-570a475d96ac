-- 1. 创建并使用 Paimon Catalog (与 json.sql 类似)
CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon', -- 与 json.sql 中的 warehouse 路径一致
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

-- 2. 创建并使用 Hive Catalog (可选，如果您也需要 Hive Catalog 的话，从 json.sql 中借鉴)
-- CREATE CATALOG hive_catalog WITH (
--  'type' = 'hive',
--  'default-database' = 'ubd_sscj_prod_flink',
--  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf' -- 与 json.sql 中的 hive-conf-dir 路径一致
-- );
-- USE CATALOG hive_catalog; -- 如果创建了 Hive Catalog，则使用它

-- 3. 设置执行参数 (与 json.sql 类似)
SET 'sql-client.execution.result-mode' = 'tableau'; -- 或 'table' / 'changelog'
SET 'yarn.application.queue' = 'hh_slfn2_sschj'; -- 与 json.sql 中的队列一致
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';
-- Flink 1.17 中 'table.exec.sink.upsert-materialize' 可能不再需要或有变化，请根据实际情况调整

-- 4. 创建 Kafka 源表 (临时表)
-- 用于从 Kafka 读取原始 JSON 字符串数据
CREATE TEMPORARY TABLE kafka_province_app_order_source (
  json_payload STRING -- 假设 Kafka 消息体就是一行 JSON 字符串
  -- 如果 Kafka 消息本身是 JSON 对象，其中一个字段包含目标 JSON 字符串，则需要调整
  -- 例如: kafka_message ROW<actual_json_payload STRING>
  -- 然后在 SELECT 中使用 kafka_message.actual_json_payload
) WITH (
  'connector' = 'kafka',
  -- TODO: 请根据您的实际 Kafka 配置替换以下参数
  'properties.bootstrap.servers' = '10.177.58.131:39092,10.177.58.132:39092,10.177.58.133:39092,10.177.58.134:39092,10.177.58.135:39092,10.177.58.136:39092,10.177.58.137:39092,10.177.58.138:39092,10.177.58.139:39092,10.177.58.140:39092,10.177.58.141:39092,10.177.58.142:39092,10.177.58.143:39092,10.177.58.144:39092,10.177.58.145:39092,10.177.58.146:39092,10.177.58.147:39092,10.177.58.148:39092,10.177.58.149:39092,10.177.58.150:39092,10.177.58.154:39092,10.177.58.155:39092,10.177.58.156:39092,10.177.58.157:39092,10.177.58.158:39092,10.177.58.159:39092,10.177.58.151:39092,10.177.58.152:39092,10.177.58.153:39092', -- 使用完整的 Kafka broker 地址列表
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.group.id' = 'rt_group_1', -- 为您的作业指定一个新的消费者组 ID
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="rt_data_system" password="Urt@ynjd07";', -- 沿用 json.sql 中的认证信息，请确认是否适用
  'topic' = 'realtime_business_analysis_sscj_qkpt_99', -- TODO: 请替换为您的实际 Kafka 主题名称
  'format' = 'raw', -- 读取每条 Kafka 消息的 value 为单个字符串
  -- 'json.ignore-parse-errors' = 'true', -- 如果 format = 'json' 时可考虑
  'scan.startup.mode' = 'earliest-offset' -- 或者 'earliest-offset', 'group-offsets', 'timestamp'
  -- 'scan.startup.timestamp-millis' = '...' -- 如果 startup.mode = 'timestamp'
);

-- 5. 创建 Paimon 目标表
-- 表名：ods_r_paimon_province_app_orderInfo
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_province_app_orderInfo` (
    OPT STRING,
    PK_ID STRING,
    ORDER_RESULT STRING,
    CHANNEL_CODE STRING,
    LOGIN_SERIAL_NUMBER STRING,
    EPARCHY_CODE STRING,
    ORDER_TYPE STRING,
    PROVINCE_CODE STRING,
    TRANS_ID STRING,
    SERIAL_NUMBER STRING,
    ORDER_ID STRING,
    COMM_NAME STRING,
    STRATEGY_ID STRING,
    ORDER_TIME TIMESTAMP(3), -- 从数据源解析的时间
    COMM_ID STRING,
    QKACT_ID STRING,
    paimon_ingest_time TIMESTAMP(3) COMMENT 'Paimon 记录写入处理时间戳', -- 用于排序和可能的调试
    PRIMARY KEY (PK_ID) NOT ENFORCED
) WITH (
  'connector' = 'paimon',
  'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/ods_r_paimon_province_app_orderInfo', -- Paimon 表的存储路径
  'auto-create' = 'true',                         -- 如果表不存在，则自动创建
  'merge-engine' = 'partial-update',              -- 使用 partial-update 合并引擎
  'sequence.field' = 'paimon_ingest_time',        -- 指定用于 partial-update 的序列字段
  'partial-update.ignore-delete' = 'true',        -- partial-update 通常忽略删除标记，如需处理删除，方式会有所不同
  'changelog-producer' = 'input'                  -- 适用于 partial-update，表示输入流自身是 changelog
  -- 以下 default-value 选项主要用于 schema evolution 时添加新列的默认值，对于现有字段的更新逻辑影响不大
  -- 'fields.ORDER_RESULT.default-value' = 'null',
  -- ... (可以为其他可空字段按需添加)
);



-- 1. 创建 Kafka 源表 (临时表) - 用于在线应用订单数据
-- 请注意：您需要根据您的实际 Kafka 主题调整 'topic' 和 'properties.group.id'。
CREATE TEMPORARY TABLE kafka_zaixian_app_order_source (
  json_payload STRING -- 假设 Kafka 消息体就是一行 JSON 字符串
) WITH (
  'connector' = 'kafka',
  -- TODO: 请确认以下 Kafka 配置是否适用于新数据源，特别是 bootstrap.servers
  'properties.bootstrap.servers' = '10.177.58.131:39092,10.177.58.132:39092,10.177.58.133:39092,10.177.58.134:39092,10.177.58.135:39092,10.177.58.136:39092,10.177.58.137:39092,10.177.58.138:39092,10.177.58.139:39092,10.177.58.140:39092,10.177.58.141:39092,10.177.58.142:39092,10.177.58.143:39092,10.177.58.144:39092,10.177.58.145:39092,10.177.58.146:39092,10.177.58.147:39092,10.177.58.148:39092,10.177.58.149:39092,10.177.58.150:39092,10.177.58.154:39092,10.177.58.155:39092,10.177.58.156:39092,10.177.58.157:39092,10.177.58.158:39092,10.177.58.159:39092,10.177.58.151:39092,10.177.58.152:39092,10.177.58.153:39092',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  -- TODO: 为您的新作业指定一个新的、唯一的消费者组 ID
  'properties.group.id' = 'rt_group_1',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="rt_data_system" password="Urt@ynjd07";',
  -- TODO: 请替换为您的实际 Kafka 主题名称
  'topic' = 'realtime_business_analysis_sscj_zxgs_innov_pro_99',
  'format' = 'raw', -- 读取每条 Kafka 消息的 value 为单个字符串
  'scan.startup.mode' = 'earliest-offset' -- 或其他适合的模式
);

-- 2. 创建 Paimon 目标表: ods_r_paimon_zaixian_app_orderInfo
-- 库名和 Catalog 沿用 json.sql 中的设定
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_app_orderInfo` (
    register_time TIMESTAMP(3),
    effective_time TIMESTAMP(3),
    trans_id STRING,
    city_code STRING,
    product_fee DECIMAL(19, 2), -- 假设产品费用为数值型，精度19，小数位2
    product_name_busi STRING,
    stlogin_mobile STRING,
    province_code STRING,
    product_id_busi STRING,
    province_name STRING,
    qkact_id STRING,
    order_code STRING,          -- 主键
    order_status STRING,
    city_name STRING,
    -- 注意: JSON 中的 "registertime" 字段与 "register_time" 在示例中值相同。
    -- 此处仅映射 "register_time"。如果 "registertime" 可能有不同值且需要保留，请添加相应列。
    product_name_cb STRING,
    table_source STRING,
    product_id_cb STRING,
    order_mobile STRING,
    order_type STRING,
    paimon_ingest_time TIMESTAMP(3) COMMENT 'Paimon 记录写入处理时间戳',
    PRIMARY KEY (order_code) NOT ENFORCED
) WITH (
  'connector' = 'paimon',
  'path' = 'hdfs:///user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink/ods_r_paimon_zaixian_app_orderInfo', -- Paimon 表的存储路径
  'auto-create' = 'true',
  'merge-engine' = 'partial-update',
  'sequence.field' = 'paimon_ingest_time',
  'partial-update.ignore-delete' = 'true',
  'changelog-producer' = 'input'
);

-- 3. 将 Kafka 数据解析并插入 Paimon 表
INSERT INTO
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_app_orderInfo` (
    register_time, effective_time, trans_id, city_code, product_fee,
    product_name_busi, stlogin_mobile, province_code, product_id_busi, province_name,
    qkact_id, order_code, order_status, city_name, product_name_cb,
    table_source, product_id_cb, order_mobile, order_type, paimon_ingest_time
)
SELECT
    TRY_CAST(TO_TIMESTAMP_LTZ(JSON_VALUE(json_payload, '$.register_time'), 'yyyyMMddHHmmss') AS TIMESTAMP(3)) AS register_time,
    TRY_CAST(TO_TIMESTAMP_LTZ(JSON_VALUE(json_payload, '$.effective_time'), 'yyyyMMddHHmmss') AS TIMESTAMP(3)) AS effective_time,
    JSON_VALUE(json_payload, '$.transId') AS trans_id,
    JSON_VALUE(json_payload, '$.city_code') AS city_code,
    TRY_CAST(JSON_VALUE(json_payload, '$.product_fee') AS DECIMAL(19, 2)) AS product_fee,
    JSON_VALUE(json_payload, '$.product_name_busi') AS product_name_busi,
    JSON_VALUE(json_payload, '$.stlogin_mobile') AS stlogin_mobile,
    JSON_VALUE(json_payload, '$.province_code') AS province_code,
    JSON_VALUE(json_payload, '$.product_id_busi') AS product_id_busi,
    JSON_VALUE(json_payload, '$.province_name') AS province_name,
    JSON_VALUE(json_payload, '$.qkactId') AS qkact_id,
    JSON_VALUE(json_payload, '$.order_code') AS order_code,
    JSON_VALUE(json_payload, '$.order_status') AS order_status,
    JSON_VALUE(json_payload, '$.city_name') AS city_name,
    JSON_VALUE(json_payload, '$.product_name_cb') AS product_name_cb,
    JSON_VALUE(json_payload, '$.table_source') AS table_source,
    JSON_VALUE(json_payload, '$.product_id_cb') AS product_id_cb,
    JSON_VALUE(json_payload, '$.order_mobile') AS order_mobile,
    JSON_VALUE(json_payload, '$.order_type') AS order_type,
    CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_ingest_time
FROM
    kafka_zaixian_app_order_source
WHERE
    JSON_VALUE(json_payload, '$.order_code') IS NOT NULL;