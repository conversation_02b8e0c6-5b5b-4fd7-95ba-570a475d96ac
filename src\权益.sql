CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
-- 创建Kafka源表，解析JSON数据

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_qy_act_id` (
  phone_number STRING,
  act_id STRING,
  create_time TIMESTAMP(3),
  status STRING,
  paimon_time TIMESTAMP(3)
) WITH (
  'connector' = 'paimon',
  'path' = 'hdfs:///paimon/ubd_sscj_prod_flink/dwa_r_qy_act_id',
  'auto-create' = 'true',
  'bucket' = '32'
);

CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_qy_act_distribution` (
  phone_number STRING,
  act_ids STRING,
  PRIMARY KEY (phone_number) NOT ENFORCED
) WITH (
  'connector' = 'upsert-kafka',
  'topic' = 'IN_KAFKA_TO_HBASE',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092',
  'key.format' = 'json',             -- 主键序列化格式
  'value.format' = 'json',           -- 值序列化格式
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";',
  'sink.parallelism' = '4'
);


-- 最终时间处理方案
INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_qy_act_distribution`/*+ OPTIONS('properties.group.id'='dwa_r_kafka_qy_act_id_20250310')*/
SELECT 
  phone_number,
  LISTAGG(DISTINCT act_id, ',') AS act_ids  
FROM (
  SELECT 
    phone_number,
    act_id
  FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_qy_act_id`
  WHERE 
    create_time >= DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyy-MM-01 00:00:00')  -- 当月1日
    AND create_time < DATE_FORMAT(CURRENT_TIMESTAMP + INTERVAL '1' MONTH, 'yyyy-MM-01 00:00:00')  -- 下月1日
    AND status <> 'N'
)
GROUP BY phone_number;  -- 最终按phone_number聚合