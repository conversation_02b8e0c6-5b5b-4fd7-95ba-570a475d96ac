#!/bin/bash

# 脚本功能：每月1日自动删除HBase中指定列簇的旧数据
# 用法: 直接执行脚本或添加到crontab定时执行，无需传递任何参数
# 推荐的crontab设置：0 0 1 * * /path/to/删除HBase旧数据_定时任务版.sh > /path/to/logs/cleanup_$(date +\%Y\%m\%d).log 2>&1

# ====== 配置区域（根据实际环境配置） ======
# HBase配置
HBASE_HOME="/usr/hdp/current/hbase-client"
HBASE_USER="hbase"
TABLE_NAME="ctg810544697531_hh_fed_sub23_lclabel:label_lc_realtime_data_test"
# 要删除的列簇，多个列簇用逗号分隔
COLUMN_FAMILIES="com1:IS_CONTRACT_USER"

# ZooKeeper配置
ZK_HOSTS="************,************,************"
ZK_PORT="2181"
ZNODE_ROOT="/hbase-unsecure"

# 日志设置
LOG_FILE="/data/disk01/hh_slfn2_sschj/private/liucz37/shell/hbase/delete.log"
# 是否启用调试模式
DEBUG=false
# ====== 配置区域结束 ======

# 函数: 记录日志
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a ${LOG_FILE}
}

# 格式化ZooKeeper地址
format_zk_quorum() {
    local hosts=$1
    local port=$2
    local result=""
    
    # 分割主机名并添加端口
    IFS=',' read -ra HOST_ARRAY <<< "$hosts"
    for i in "${HOST_ARRAY[@]}"; do
        # 检查主机名是否已包含端口
        if [[ $i == *":"* ]]; then
            result="${result}${i},"
        else
            result="${result}${i}:${port},"
        fi
    done
    
    # 移除最后的逗号
    result=${result%,}
    echo $result
}

# 检查是否为每月1日
CURRENT_DAY=$(date +%d)
# if [ "$CURRENT_DAY" != "01" ]; then
#     log "今天不是每月1日，跳过执行。"
#     log "若要强制执行，请手动编辑脚本临时注释掉此检查逻辑。"
#     exit 0
# fi

# 检查HBase客户端是否存在
if [ ! -d "$HBASE_HOME" ]; then
    log "错误: HBase客户端目录不存在: $HBASE_HOME"
    exit 1
fi

# 格式化ZooKeeper地址
ZOOKEEPER_QUORUM=$(format_zk_quorum "$ZK_HOSTS" "$ZK_PORT")
log "ZooKeeper地址: $ZOOKEEPER_QUORUM"

# 先测试列簇访问
log "测试列簇访问方式..."
CF_TEST_SCRIPT="/tmp/cf_test_script_$(date +\%s).txt"
CF_TO_TEST="${COLUMN_FAMILIES%%,*}"  # 取第一个列簇测试
CF_BASE="${CF_TO_TEST%%:*}"          # 获取列簇基本名称

# 测试不同的列簇访问方式
cat > ${CF_TEST_SCRIPT} << EOF
# 测试1: 完整列簇名包含限定符 (com1:IS_CONTRACT_USER)
scan '${TABLE_NAME}', {COLUMNS => '${CF_TO_TEST}', LIMIT => 5}
# 测试2: 仅列簇名称 (com1)
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', LIMIT => 5}
# 测试3: 使用数组形式 ['com1:IS_CONTRACT_USER']
scan '${TABLE_NAME}', {COLUMNS => ['${CF_TO_TEST}'], LIMIT => 5}
# 测试4: 使用数组形式 ['com1']
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}'], LIMIT => 5}
# 测试5: 使用数组形式 ['com1:']
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}:'], LIMIT => 5}
EOF

log "执行列簇访问测试..."
CF_TEST_OUTPUT=$($HBASE_HOME/bin/hbase shell ${CF_TEST_SCRIPT} 2>&1)
log "测试结果输出:"
echo "$CF_TEST_OUTPUT"

# 测试哪种列簇访问方式有效
TEST1_ROWS=$(echo "$CF_TEST_OUTPUT" | grep -A10 "测试1:" | grep -c "row=")
TEST2_ROWS=$(echo "$CF_TEST_OUTPUT" | grep -A10 "测试2:" | grep -c "row=")
TEST3_ROWS=$(echo "$CF_TEST_OUTPUT" | grep -A10 "测试3:" | grep -c "row=")
TEST4_ROWS=$(echo "$CF_TEST_OUTPUT" | grep -A10 "测试4:" | grep -c "row=")
TEST5_ROWS=$(echo "$CF_TEST_OUTPUT" | grep -A10 "测试5:" | grep -c "row=")

log "测试1 (COLUMNS => '${CF_TO_TEST}'): 找到 $TEST1_ROWS 行"
log "测试2 (COLUMNS => '${CF_BASE}'): 找到 $TEST2_ROWS 行" 
log "测试3 (COLUMNS => ['${CF_TO_TEST}']): 找到 $TEST3_ROWS 行"
log "测试4 (COLUMNS => ['${CF_BASE}']): 找到 $TEST4_ROWS 行"
log "测试5 (COLUMNS => ['${CF_BASE}:']): 找到 $TEST5_ROWS 行"

# 确定最有效的访问方式
BEST_METHOD=0
BEST_COUNT=0
if [ $TEST1_ROWS -gt $BEST_COUNT ]; then BEST_METHOD=1; BEST_COUNT=$TEST1_ROWS; fi
if [ $TEST2_ROWS -gt $BEST_COUNT ]; then BEST_METHOD=2; BEST_COUNT=$TEST2_ROWS; fi
if [ $TEST3_ROWS -gt $BEST_COUNT ]; then BEST_METHOD=3; BEST_COUNT=$TEST3_ROWS; fi
if [ $TEST4_ROWS -gt $BEST_COUNT ]; then BEST_METHOD=4; BEST_COUNT=$TEST4_ROWS; fi
if [ $TEST5_ROWS -gt $BEST_COUNT ]; then BEST_METHOD=5; BEST_COUNT=$TEST5_ROWS; fi

if [ $BEST_COUNT -eq 0 ]; then
    log "警告: 所有测试方法都无法找到数据，将尝试按列簇基本名称扫描整个表"
    BEST_METHOD=4  # 默认使用方法4
fi

log "确定最佳访问方式为方法 $BEST_METHOD"
rm -f ${CF_TEST_SCRIPT}

# 创建临时配置文件
TEMP_CONF_DIR=$(mktemp -d)
log "创建临时配置目录: $TEMP_CONF_DIR"

cat > $TEMP_CONF_DIR/hbase-site.xml << EOL
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
    <property>
        <name>hbase.zookeeper.quorum</name>
        <value>${ZOOKEEPER_QUORUM}</value>
    </property>
    <property>
        <name>zookeeper.znode.parent</name>
        <value>${ZNODE_ROOT}</value>
    </property>
    <property>
        <name>hbase.client.keyvalue.maxsize</name>
        <value>10485760</value>
    </property>
    <property>
        <name>hbase.rpc.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.operation.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.scanner.timeout.period</name>
        <value>120000</value>
    </property>
    <property>
        <name>zookeeper.session.timeout</name>
        <value>120000</value>
    </property>
    <property>
        <name>hbase.client.retries.number</name>
        <value>10</value>
    </property>
</configuration>
EOL

# 设置环境变量
export HBASE_HOME
export PATH=$HBASE_HOME/bin:$PATH
export HADOOP_USER_NAME=${HBASE_USER}
export HBASE_CONF_DIR=$TEMP_CONF_DIR

if [ "$DEBUG" = true ]; then
    log "调试信息:"
    log "  HBASE_HOME = $HBASE_HOME"
    log "  HBASE_CONF_DIR = $HBASE_CONF_DIR"
    log "  HADOOP_USER_NAME = $HADOOP_USER_NAME"
    log "  ZooKeeper = $ZOOKEEPER_QUORUM"
    log "  ZNode根路径 = $ZNODE_ROOT"
    log "  表名 = $TABLE_NAME"
    log "  列簇清单 = $COLUMN_FAMILIES"
    log "  配置文件内容:"
    cat $TEMP_CONF_DIR/hbase-site.xml
    
    # 尝试列出表
    log "尝试列出HBase表:"
    $HBASE_HOME/bin/hbase shell << EOF
list
EOF
fi

# 获取上个月的日期
LAST_MONTH=$(date -d "last month" +%Y%m)
log "开始删除 ${LAST_MONTH} 之前的数据，表: ${TABLE_NAME}"

# 处理多个列簇
IFS=',' read -ra COLUMN_FAMILY_ARRAY <<< "$COLUMN_FAMILIES"
log "要删除的列簇数量: ${#COLUMN_FAMILY_ARRAY[@]}"

# 先执行一个简单查询检查表是否存在并有数据
TEST_SCRIPT="/tmp/hbase_test_script_$(date +\%s).txt"
cat > ${TEST_SCRIPT} << EOF
exists '${TABLE_NAME}'
count '${TABLE_NAME}', { INTERVAL => 100000, CACHE => 10000 }
describe '${TABLE_NAME}'
EOF

log "测试表是否存在并获取基本信息..."
$HBASE_HOME/bin/hbase shell ${TEST_SCRIPT}
TEST_RESULT=$?
if [ $TEST_RESULT -ne 0 ]; then
    log "错误: 访问表 ${TABLE_NAME} 失败，请检查表名和连接配置"
    exit 1
fi
rm -f ${TEST_SCRIPT}

# 表行数太多，必须分段处理
log "检测到表数据量很大，将使用分段扫描和处理策略"

for CF in "${COLUMN_FAMILY_ARRAY[@]}"; do
    log "处理列簇: $CF"
    
    # 先检查列簇是否存在
    FAMILY_CHECK_SCRIPT="/tmp/hbase_cf_check_$(date +\%s)_${CF//:/}.txt"
    cat > ${FAMILY_CHECK_SCRIPT} << EOF
describe '${TABLE_NAME}'
EOF

    log "检查列簇 ${CF} 是否存在..."
    FAMILY_EXISTS=$($HBASE_HOME/bin/hbase shell ${FAMILY_CHECK_SCRIPT} 2>&1 | grep -c "${CF}")
    # 提取列簇基本名称（不含限定符）
    CF_BASE="${CF%%:*}"  # 获取冒号前面部分
    if [ "$FAMILY_EXISTS" -eq 0 ]; then
        log "警告: 列簇 ${CF} 在表描述中未找到，尝试使用基本列簇名称: ${CF_BASE}"
    else
        log "列簇 ${CF} 在表 ${TABLE_NAME} 中存在"
    fi
    rm -f ${FAMILY_CHECK_SCRIPT}

    # 执行分段扫描
    MAX_SEGMENTS=20        # 最大分段数量
    SEGMENT_SIZE=1000000   # 每段处理的最大行数
    TOTAL_PROCESSED=0      # 已处理的总行数
    
    log "开始分段扫描和处理 ${CF} 列簇数据，每段最多处理 ${SEGMENT_SIZE} 行"

    for ((segment=0; segment<MAX_SEGMENTS; segment++)); do
        START_ROW=""  # 初始为空，表示从表开始处扫描
        
        # 如果不是第一段，则从上一段的最后一个rowkey之后开始
        if [ $segment -gt 0 ] && [ -n "$LAST_ROW" ]; then
            START_ROW="STARTROW => '$LAST_ROW'"
        fi
        
        # 创建扫描脚本 - 根据测试确定的最佳方法
        SCAN_SCRIPT="/tmp/hbase_scan_segment_${segment}_$(date +\%s)_${CF//:/}.txt"
        
        case $BEST_METHOD in
            1) # 完整列簇名
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF}', LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF}', ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
            2) # 仅列簇名称
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
            3) # 数组形式完整列簇
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF}'], LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF}'], ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
            4) # 数组形式列簇名
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}'], LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}'], ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
            5) # 数组形式带冒号
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}:'], LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => ['${CF_BASE}:'], ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
            *)
                log "使用默认方法"
                if [ -z "$START_ROW" ]; then
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                else
                    cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', ${START_ROW}, LIMIT => ${SEGMENT_SIZE}, CACHE => 10000}
EOF
                fi
                ;;
        esac
        
        log "执行第 $((segment+1)) 段扫描 (最多 ${SEGMENT_SIZE} 行)..."
        if [ "$DEBUG" = true ]; then
            log "扫描命令: $(cat ${SCAN_SCRIPT})"
        fi
        
        # 执行扫描并捕获输出
        SCAN_OUTPUT=$($HBASE_HOME/bin/hbase shell ${SCAN_SCRIPT} 2>&1)
        SCAN_RESULT=$?
        
        if [ $SCAN_RESULT -ne 0 ]; then
            log "警告: 扫描失败，尝试使用备用方法进行扫描..."
            # 尝试使用通用的扫描方法 - 仅扫描列簇名称
            cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', LIMIT => ${SEGMENT_SIZE}, CACHE => 10000 ${START_ROW:+, $START_ROW}}
EOF
            SCAN_OUTPUT=$($HBASE_HOME/bin/hbase shell ${SCAN_SCRIPT} 2>&1)
            SCAN_RESULT=$?
            
            if [ $SCAN_RESULT -ne 0 ]; then
                # 尝试使用通配符过滤器
                cat > ${SCAN_SCRIPT} << EOF
scan '${TABLE_NAME}', {FILTER => "ColumnPrefixFilter('${CF##*:}')", LIMIT => ${SEGMENT_SIZE}, CACHE => 10000 ${START_ROW:+, $START_ROW}}
EOF
                SCAN_OUTPUT=$($HBASE_HOME/bin/hbase shell ${SCAN_SCRIPT} 2>&1)
                SCAN_RESULT=$?
                
                if [ $SCAN_RESULT -ne 0 ]; then
                    log "错误: 无法使用任何方法扫描列簇 ${CF}，跳过处理该列簇"
                    if [ "$DEBUG" = true ]; then
                        ERROR_DUMP="/tmp/hbase_scan_error_$(date +\%s).txt"
                        echo "$SCAN_OUTPUT" > $ERROR_DUMP
                        log "错误输出已保存到 $ERROR_DUMP"
                    fi
                    rm -f ${SCAN_SCRIPT}
                    continue
                fi
            fi
        fi
        
        # 提取rowkey
        ROWKEYS=$(echo "$SCAN_OUTPUT" | grep -E "row=|rowkey=" | awk -F"=" '{print $2}' | awk -F"," '{print $1}' | tr -d "'" | tr -d '"' | sort | uniq)
        
        # 获取最后一个rowkey，用于下一段的起始点
        LAST_ROW=$(echo "$ROWKEYS" | tail -n 1)
        
        # 计算本段获取的rowkey数量
        ROWKEY_COUNT=$(echo "$ROWKEYS" | grep -v "^$" | wc -l)
        log "本段获取到 ${ROWKEY_COUNT} 个rowkey"
        
        # 如果本段没有数据，说明已经处理完所有数据，终止循环
        if [ "$ROWKEY_COUNT" -eq 0 ]; then
            log "没有更多的数据需要处理，结束分段处理"
            rm -f ${SCAN_SCRIPT}
            break
        fi
        
        # 创建删除脚本
        DELETE_SCRIPT="/tmp/hbase_delete_segment_${segment}_$(date +\%s)_${CF//:/}.txt"
        echo "# 批量删除脚本 - 段 $((segment+1))" > ${DELETE_SCRIPT}
        echo "# 表名: ${TABLE_NAME}" >> ${DELETE_SCRIPT}
        echo "# 列簇: ${CF}" >> ${DELETE_SCRIPT}
        echo "# 生成时间: $(date)" >> ${DELETE_SCRIPT}
        echo "" >> ${DELETE_SCRIPT}
        echo "# 初始化批量删除数组" >> ${DELETE_SCRIPT}
        echo "deleteall_batch = []" >> ${DELETE_SCRIPT}
        
        # 添加每个rowkey到批量删除数组
        BATCH_SIZE=1000  # 每批次处理的行数
        CURRENT_BATCH=0
        CURRENT_COUNT=0
        
        # 添加Java类引用，确保能正确使用字节工具类
        echo "import org.apache.hadoop.hbase.util.Bytes" >> ${DELETE_SCRIPT}
        echo "" >> ${DELETE_SCRIPT}

        # 对于每一行rowkey
        echo "$ROWKEYS" | while read -r rowkey; do
            # 跳过空行
            if [ -z "$rowkey" ]; then
                continue
            fi
            
            echo "delete = org.apache.hadoop.hbase.client.Delete.new('$rowkey')" >> ${DELETE_SCRIPT}
            echo "delete.addFamily(Bytes.toBytes('${CF_BASE}'))" >> ${DELETE_SCRIPT}  # 只使用列簇基本名称
            echo "deleteall_batch.push(delete)" >> ${DELETE_SCRIPT}
            CURRENT_COUNT=$((CURRENT_COUNT + 1))
            
            # 每1000个rowkey提交一次批量删除
            if [ $((CURRENT_COUNT % BATCH_SIZE)) -eq 0 ]; then
                CURRENT_BATCH=$((CURRENT_BATCH + 1))
                echo "# 提交批次 $CURRENT_BATCH (${CURRENT_COUNT}个rowkey)" >> ${DELETE_SCRIPT}
                echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
                echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
                echo "puts \"已删除 ${CURRENT_COUNT} 个rowkey...\"" >> ${DELETE_SCRIPT}
                echo "deleteall_batch = []" >> ${DELETE_SCRIPT}
            fi
        done
        
        # 处理剩余的rowkey
        if [ $((CURRENT_COUNT % BATCH_SIZE)) -ne 0 ]; then
            CURRENT_BATCH=$((CURRENT_BATCH + 1))
            echo "# 提交最后一个批次 $CURRENT_BATCH" >> ${DELETE_SCRIPT}
            echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
            echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
        fi
        
        echo "puts \"本段总共删除 ${CURRENT_COUNT} 个rowkey的 ${CF} 列簇数据，完成!\"" >> ${DELETE_SCRIPT}
        echo "exit" >> ${DELETE_SCRIPT}
        
        # 如果调试模式，显示脚本内容
        if [ "$DEBUG" = true ]; then
            log "删除脚本内容:"
            cat ${DELETE_SCRIPT}
        fi
        
        # 执行删除操作
        log "执行第 $((segment+1)) 段数据的删除操作..."
        DELETE_OUTPUT=$($HBASE_HOME/bin/hbase shell ${DELETE_SCRIPT} 2>&1)
        DELETE_RESULT=$?
        
        if [ $DELETE_RESULT -eq 0 ]; then
            TOTAL_PROCESSED=$((TOTAL_PROCESSED + CURRENT_COUNT))
            log "第 $((segment+1)) 段成功删除 ${CURRENT_COUNT} 个rowkey的 ${CF} 列簇数据"
        else
            log "警告: 第 $((segment+1)) 段删除操作失败，退出码: $DELETE_RESULT"
            log "失败原因可能是: "
            echo "$DELETE_OUTPUT" | grep -i "error\|exception\|failed"
            log "将继续处理下一段数据..."
        fi
        
        # 清理临时文件
        rm -f ${SCAN_SCRIPT}
        rm -f ${DELETE_SCRIPT}
        
        # 检查是否已处理完所有数据
        if [ "$ROWKEY_COUNT" -lt "$SEGMENT_SIZE" ]; then
            log "本段行数 (${ROWKEY_COUNT}) 小于分段大小 (${SEGMENT_SIZE})，数据处理完毕"
            break
        fi
        
        # 检查是否达到最大分段数
        if [ $segment -eq $((MAX_SEGMENTS-1)) ]; then
            log "已达到最大分段数 ${MAX_SEGMENTS}，停止处理。如需继续处理，请再次运行脚本"
        fi
    done
    
    log "列簇 ${CF} 的处理完成，共处理 ${TOTAL_PROCESSED} 行数据"
done

rm -rf ${TEMP_CONF_DIR}

log "清理操作完成"
exit 0 