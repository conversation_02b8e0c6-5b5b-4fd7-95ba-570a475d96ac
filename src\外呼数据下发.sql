CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer'='DROP';
SET
  'table.exec.sink.upsert-materialize'='NONE';

--下发kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_tf_order_driver_xkf_call_info` (
  INFO_ID STRING,
  CITY_CODE STRING,
  RECORD_FILE_NAME STRING,
  START_TIME STRING,
  CALL_ID STRING,
  SINGLE_CALL STRING,
  CALL_RESULT STRING,
  CALLER_SERIAL_NUMBER STRING,
  CALL_STATUS STRING,
  USER_ID STRING,
  BUSINESS_BELONG_CODE STRING,
  UPDATE_TIME STRING,
  PROVINCE_CODE STRING,
  PROVINCE_NAME STRING,
  RECORD_FILE_DIR STRING,
  CALLED_SERIAL_NUMBER STRING,
  STRATEGY_ID STRING,
  PK_ID STRING,
  CALL_TYPE STRING,
  CREATE_TIME STRING,
  DURATION STRING,
  TASK_ID STRING,
  QUALITY_STATUS STRING,
  OPT STRING,
  OPTTIME STRING,
  CONSTRAINT INFO_ID PRIMARY KEY (INFO_ID) NOT ENFORCED
) WITH (
  'connector' = 'upsert-kafka',
  'topic' = 'ORDER_DRIVER_XKF_CALL',
  'key.format' = 'json',
  'value.format' = 'json',
  'properties.bootstrap.servers' = '10.177.29.46:9095,10.177.29.47:9095,10.177.29.48:9095,10.177.29.49:9095,10.177.29.50:9095,10.177.29.51:9095,10.177.29.52:9095,10.177.29.53:9095,10.177.29.54:9095,10.177.29.55:9095,10.177.29.56:9095,10.177.29.57:9095,10.177.29.58:9095,10.177.29.59:9095,10.177.29.60:9095,10.177.29.61:9095,10.177.29.62:9095,10.177.29.63:9095,10.177.29.64:9095,10.177.29.65:9095,10.177.29.66:9095,10.177.29.67:9095,10.177.29.68:9095,10.177.29.69:9095,10.177.29.70:9095,10.177.29.71:9095,10.177.29.73:9095,10.177.29.73:9095,10.177.29.74:9095,10.177.29.75:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = ''
);

insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_tf_order_driver_xkf_call_info`
select
  INFO_ID,
  CITY_CODE,
  RECORD_FILE_NAME,
  START_TIME,
  CALL_ID,
  SINGLE_CALL,
  CALL_RESULT,
  CALLER_SERIAL_NUMBER,
  CALL_STATUS,
  USER_ID,
  BUSINESS_BELONG_CODE,
  UPDATE_TIME,
  PROVINCE_CODE,
  PROVINCE_NAME,
  RECORD_FILE_DIR,
  CALLED_SERIAL_NUMBER,
  STRATEGY_ID,
  PK_ID,
  CALL_TYPE,
  CREATE_TIME,
  DURATION,
  TASK_ID,
  QUALITY_STATUS,
  opt,
  opttime
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_order_driver_xkf_call_info` /*+ OPTIONS('properties.group.id'='dwa_r_kafka_xkf_call_20250808','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
  where
  PROVINCE_CODE = '34';
