<optimized_prompt>
<task>开发一个基于DeepSeek的智能体，能够读取和分析大文件数据，并通过自然语言描述获取解析结果</task>

<context>
想做一个基于deepseek的智能体，部署在dify上，功能是具备读取大文件（txt、Excel、csv等文本格式）进行数据分析，可以通过文字描述，获取到想要的解析数据，不用再写sql或者其他方式实现需求。比如输入数据要求，广东省有多少条数据，读取通话信息或者短信记录，分析出哪些人想办理宽带业务，哪些人需要流量加油包，分析用户流量使用情况，对数据进行统计，并能输出计算逻辑方便代码实现。
</context>

<instructions>
1. 开发基于DeepSeek的智能体核心功能：
   - 实现大文件读取能力，支持txt、Excel、csv等文本格式
   - 设计自然语言解析模块，理解用户的数据分析需求
   - 构建数据分析引擎，能够执行常见的数据统计操作

2. 实现具体分析功能：
   - 基础统计功能：
     * 区域数据计数（按省/市/区县分级统计）
     * 数据分布分析（频次、占比、趋势）
     * 异常值检测与处理
   - 通话记录和短信内容分析：
     * 关键词提取与情感分析
     * 会话主题分类
     * 交互模式识别
   - 用户需求分类：
     * 基于文本内容的意图识别模型
     * 多标签分类（宽带业务、流量加油包等）
     * 需求优先级评估
   - 用户流量使用情况分析：
     * 使用时段分布
     * 流量消耗模式分类
     * 异常使用行为检测
   - 高级分析功能：
     * 用户画像构建
     * 关联规则挖掘
     * 预测性分析模型

3. 开发结果输出模块：
   - 生成可视化数据报告
   - 输出计算逻辑说明
   - 提供代码实现建议

4. 部署到Dify平台：
   - 配置API接口
   - 设计用户交互界面
   - 优化性能以适应大文件处理

5. 测试与优化：
   - 验证各功能模块
   - 优化自然语言理解准确率
   - 提升大数据处理效率
</instructions>

<output_format>
1. 完整的智能体设计方案文档，包含：
   - 架构图
   - 功能模块说明
   - 技术实现方案

2. 可执行的代码实现，包含：
   - 文件读取模块
   - 自然语言处理模块
   - 数据分析引擎
   - 结果输出模块

3. 部署指南：
   - Dify平台配置说明
   - API接口文档
   - 使用示例

4. 测试报告：
   - 功能测试结果
   - 性能测试数据
   - 优化建议
</output_format>
</optimized_prompt>