CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';

-- 新的Kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_xinAn_zzftInfo` (
  resumptionPhone STRING PRIMARY KEY NOT ENFORCED,
  resumptionStatus STRING
) WITH (
  'connector' = 'upsert-kafka',
  'topic' = 'IN_KAFKA_TO_HBASE',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092',
  'key.format' = 'json',
  'value.format' = 'json',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";'
);

-- 新的Paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_XinAn_ZiZhuFuTong` (
  provinceId string,
  cityId string,
  resumptionPhone string,
  shutdownTime string,
  resumptionTime string,
  resumptionStatus string,
  opertype string,
  event_time TIMESTAMP(3) COMMENT '事件时间戳',
  kafka_time TIMESTAMP(3) COMMENT 'kafka时间戳',
  paimon_time TIMESTAMP(3) COMMENT '写入时间戳',
  headers MAP < STRING,
  BYTES >,
  PRIMARY KEY (resumptionPhone) NOT ENFORCED
) WITH (
  'consumer.expiration-time' = '72h',
  'log.scan.remove-normalize' = 'true',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'num-sorted-run.stop-trigger' = '2147483647',
  'sort-spill-threshold' = '10',
  'snapshot.expire.execution-mode' = 'async',
  'record-level.expire-time' = '60d',
  'record-level.time-field' = 'paimon_time'
);

-- 入湖
insert into
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_xinAn_zzftInfo`
select
  resumptionPhone,
  CASE WHEN resumptionStatus IN ('0') THEN 'TRUE' ELSE 'FALSE' END
from
  `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_XinAn_ZiZhuFuTong`
  /*+ OPTIONS('properties.group.id'='dwa_r_kafka_xinAn_zzftInfo_20250512','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;