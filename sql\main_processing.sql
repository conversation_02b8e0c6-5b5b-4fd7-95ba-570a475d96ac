-- 注册自定义聚合函数（处理无序拼接）
CREATE TEMPORARY FUNCTION ConcatDistinct AS 'com.example.udf.ConcatDistinct';

INSERT INTO act_id_result
SELECT 
  phone_number,
  ConcatDistinct(act_id) AS act_ids
FROM (
  SELECT 
    act_id,
    phone_number,
    ROW_NUMBER() OVER (PARTITION BY phone_number, act_id ORDER BY paimon_time DESC) AS rn
  FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_qy_act_id`
  WHERE 
    create_time >= DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyy-MM-01 00:00:00')
    AND create_time < DATE_FORMAT(TIMESTAMPADD(MONTH, 1, CURRENT_TIMESTAMP), 'yyyy-MM-01 00:00:00')
    AND status != 'N'
)
WHERE rn = 1
GROUP BY phone_number; 