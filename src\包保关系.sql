CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
CREATE FUNCTION IF NOT EXISTS COP_SPLIT_INDEX AS 'com.chinaunicom.rts.cop.udf.SplitIndexFunction';
--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_tf_f_user` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  HEADERS MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.24.101:9092,10.177.24.102:9092,10.177.24.103:9092,10.177.24.104:9092,10.177.24.105:9092,10.177.24.106:9092,10.177.24.107:9092,10.177.24.108:9092,10.177.24.109:9092,10.177.24.110:9092,10.177.25.66:9092,10.177.25.67:9092,10.177.25.68:9092,10.177.25.69:9092,10.177.25.70:9092,10.177.25.71:9092,10.177.25.72:9092,10.177.25.73:9092,10.177.25.74:9092,10.177.25.75:9092',
  'topic' = 'CB_TF_F_USER',
  'scan.topic-partition-discovery.interval' = '30000',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);
--paimon表
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` (
  PARTITION_ID STRING,
  USER_ID STRING,
  CUST_ID STRING,
  USECUST_ID STRING,
  BRAND_CODE STRING,
  PRODUCT_ID STRING,
  EPARCHY_CODE STRING,
  CITY_CODE STRING,
  USER_PASSWD STRING,
  USER_DIFF_CODE STRING,
  USER_TYPE_CODE STRING,
  SERIAL_NUMBER STRING,
  NET_TYPE_CODE STRING,
  SCORE_VALUE STRING,
  CREDIT_CLASS STRING,
  BASIC_CREDIT_VALUE STRING,
  CREDIT_VALUE STRING,
  ACCT_TAG STRING,
  PREPAY_TAG STRING,
  IN_DATE STRING,
  OPEN_DATE STRING,
  OPEN_MODE STRING,
  OPEN_DEPART_ID STRING,
  OPEN_STAFF_ID STRING,
  IN_DEPART_ID STRING,
  IN_STAFF_ID STRING,
  REMOVE_TAG STRING,
  DESTROY_TIME STRING,
  REMOVE_EPARCHY_CODE STRING,
  REMOVE_CITY_CODE STRING,
  REMOVE_DEPART_ID STRING,
  REMOVE_REASON_CODE STRING,
  PRE_DESTROY_TIME STRING,
  FIRST_CALL_TIME STRING,
  LAST_STOP_TIME STRING,
  USER_STATE_CODESET STRING,
  MPUTE_MONTH_FEE STRING,
  MPUTE_DATE STRING,
  UPDATE_TIME STRING,
  ASSURE_CUST_ID STRING,
  ASSURE_TYPE_CODE STRING,
  ASSURE_DATE STRING,
  DEVELOP_STAFF_ID STRING,
  DEVELOP_DATE STRING,
  DEVELOP_EPARCHY_CODE STRING,
  DEVELOP_CITY_CODE STRING,
  DEVELOP_DEPART_ID STRING,
  DEVELOP_NO STRING,
  REMARK STRING,
  CREDIT_RULE_ID STRING,
  CONTRACT_ID STRING,
  CHANGEUSER_DATE STRING,
  IN_NET_MODE STRING,
  PRODUCT_TYPE_CODE STRING,
  MAIN_DISCNT_CODE STRING,
  PRODUCT_SPEC STRING,
  PROVINCE_CODE STRING,
  OPT STRING,
  OPTTIME STRING,
  CDHTIME STRING,
  DATASOURCE STRING,
  IN_TIME STRING,
  DATABASE_TAG STRING,
  EVENT_TIME TIMESTAMP(3) NOT NULL COMMENT '事件时间戳',
  KAFKA_TIME TIMESTAMP(3) NOT NULL COMMENT 'kafka时间戳',
  PAIMON_TIME TIMESTAMP(3) NOT NULL COMMENT '写入时间戳',
  HEADERS MAP<STRING,BYTES>,
  PRIMARY KEY (USER_ID) NOT ENFORCED
) WITH (
  'bucket' = '256',
  'bucket-key' = 'USER_ID',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '48h',
  'write-only' = 'true',
  'snapshot.time-retained' = '1h'
);

CREATE TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_human_user_wide` (
  `OPT` VARCHAR(2147483647),
  `USER_ID` VARCHAR(2147483647) NOT NULL,
  `SERIAL_NUMBER` VARCHAR(2147483647),
  `PROVINCE_CODE` VARCHAR(2147483647),
  `EPARCHY_CODE` VARCHAR(2147483647),
  `SYS_CODE` VARCHAR(2147483647),
  `PRODUCT_ID` VARCHAR(2147483647),
  `OPEN_DATE` VARCHAR(2147483647),
  `BRAND_NAME` VARCHAR(2147483647),
  `USER_STATE` VARCHAR(2147483647),
  `NET_TYPE_CODE` VARCHAR(2147483647),
  `DEVELOP_DEPART_ID` VARCHAR(2147483647),
  `CUST_ID` VARCHAR(2147483647),
  `CITY_CODE` VARCHAR(2147483647),
  `OPERATOR_ID` VARCHAR(2147483647),
  `PREPAY_TAG` VARCHAR(2147483647),
  `REMOVE_TAG_USER` VARCHAR(2147483647),
  `DESTROY_TIME` VARCHAR(2147483647),
  `IN_DATE` VARCHAR(2147483647),
  `DEPART_NAME` VARCHAR(2147483647),
  `CHANNEL_ID` VARCHAR(2147483647),
  `INSTALL_ADDR` VARCHAR(2147483647),
  `INSTALLED_ADDRESS` VARCHAR(2147483647),
  `TAG_CODE` VARCHAR(2147483647),
  `USE_CUST_NAME` VARCHAR(2147483647),
  `USE_CUST_PSPT_TYPE` VARCHAR(2147483647),
  `USE_CUST_PSPT_CODE` VARCHAR(2147483647),
  `DISCNT_DETAIL` VARCHAR(2147483647),
  `ABNORMAL_FLAG` VARCHAR(2147483647),
  `TRI_TABLE` VARCHAR(2147483647),
  `KAFKA_TIME` TIMESTAMP(6),
  `SOURCE_PAIMON_TIME` TIMESTAMP(6),
  `PAIMON_TIME` TIMESTAMP(6),
  CONSTRAINT `PK_USER_ID` PRIMARY KEY (`USER_ID`) NOT ENFORCED
) WITH (
  'bucket' = '256',
  'path' = 'hdfs:/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/dwa_r_paimon_human_user_wide',
  'snapshot.time-retained' = '5h',
  'sequence.auto-padding' = 'row-kind-flag',
  'write-only' = 'true',
  'bucket-key' = 'USER_ID',
  'changelog-producer' = 'input',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h'
);
