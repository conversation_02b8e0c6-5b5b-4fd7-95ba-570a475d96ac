CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';

-- 新的Kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_Cb_Jf_Score` (
  RAW_FIELD STRING,
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp'
) WITH (
  'connector' = 'kafka',
  'topic' = 'test-cb-jf',  -- 替换为实际topic
  'properties.bootstrap.servers' = '10.177.24.121:9094,10.177.24.122:9094,10.177.24.123:9094,10.177.24.124:9094,10.177.24.125:9094,10.177.24.126:9094,10.177.24.127:9094,10.177.24.128:9094,10.177.24.129:9094,10.177.24.130:9094,10.177.24.131:9094,10.177.24.132:9094,10.177.24.133:9094,10.177.24.134:9094,10.177.24.135:9094,10.177.24.136:9094,10.177.24.137:9094,10.177.24.138:9094,10.177.24.139:9094,10.177.24.140:9094',  -- 替换为实际broker
  'scan.startup.mode' = 'earliest-offset',
  'format' = 'raw'
);

CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_Cb_Jf_Score` (
  SERIAL_NUMBER STRING,
  TELESCORE INT,
  AWARDSCORE INT,
  TOTELSCORE INT,
  paimon_time TIMESTAMP(3),
  PRIMARY KEY (SERIAL_NUMBER) NOT ENFORCED
) WITH (
  'file.format' = 'avro',
  'record-level.expire-time' = '60d',
  'record-level.time-field' = 'paimon_time'
);



CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_Cb_Jf_Score_hbase` (
  serial_number STRING,
  TELESCORE INT,
  AWARDSCORE INT,
  TOTELSCORE INT
) WITH (
  'connector' = 'kafka',
  'topic' = 'IN_KAFKA_TO_HBASE',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092',
  'key.format' = 'json',
  'value.format' = 'json',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";'
);

CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_Cb_Jf_Score_quanke` (
  serial_number STRING,
  TELESCORE INT,
  AWARDSCORE INT,
  TOTELSCORE INT
)
WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092',
  'topic' = 'ZT_STRATEGY_9900_DTS01',
  'properties.session.timeout.ms' = '300000',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'properties.sasl.mechanism' = 'PLAIN',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.plain.PlainLoginModule required username="context" password="context";',
  'key.format' = 'json',
  'key.fields' = 'serial_number',
  'value.format' = 'json',
  'key.json.ignore-parse-errors' = 'true',
  'key.json.fail-on-missing-field' = 'false',
  'value.json.ignore-parse-errors' = 'true',
  'value.json.fail-on-missing-field' = 'false'
  -- 'format' = 'csv',
  -- 'csv.ignore-parse-errors' = 'true',
  -- 'csv.field-delimiter' = '\u0001'
);

-- 入湖
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_Cb_Jf_Score`
SELECT
  JSON_VALUE(RAW_FIELD, '$.SERIAL_NUMBER') AS SERIAL_NUMBER,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) AS TELESCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS AWARDSCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) + CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS TOTELSCORE,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time
FROM
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_Cb_Jf_Score`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_Score_paimon_20240508','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;

INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_Cb_Jf_Score_quanke`
SELECT
  JSON_VALUE(RAW_FIELD, '$.SERIAL_NUMBER') AS SERIAL_NUMBER,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) AS TELESCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS AWARDSCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) + CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS TOTELSCORE,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time
FROM
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_Cb_Jf_Score`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_Score_quanke_20240508','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;

INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_Cb_Jf_Score_hbase` 
SELECT
  JSON_VALUE(RAW_FIELD, '$.SERIAL_NUMBER') AS SERIAL_NUMBER,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) AS TELESCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS AWARDSCORE,
  CAST(JSON_VALUE(RAW_FIELD, '$.TELESCORE') AS INT) + CAST(JSON_VALUE(RAW_FIELD, '$.AWARDSCORE') AS INT) AS TOTELSCORE,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS paimon_time
FROM
  `hive_catalog`.`ubd_sscj_prod_flink`.`dwd_r_kafka_Cb_Jf_Score`
  /*+ OPTIONS('properties.group.id'='dwd_r_kafka_Score_hbase_20240508','scan.startup.mode'='earliest-offset','properties.enable.auto.commit'='true','properties.auto.offset.reset.strategy'='latest','properties.auto.commit.interval.ms'='1000')*/
;