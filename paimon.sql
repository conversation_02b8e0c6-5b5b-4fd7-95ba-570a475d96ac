-- 创建Paimon Catalog
CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';


-- 创建目标表（自动创建表结构，根据业务需求调整）
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`DM_T_USER_BOARD_ACT_INFO` (
  USER_ID STRING,
  PRODUCT_ID STRING,
  PRODUCT_NAME STRING,
  ACT_TYPE STRING,
  START_DATE TIMESTAMP(3),
  END_DATE TIMESTAMP(3),
  ITEM_ID STRING,
  UPDATE_TIME TIMESTAMP(3),
  DATA_TYPE STRING,
  PRODUCT_EXPLAIN STRING,
  M_RENT_FEE STRING,
  PRIMARY KEY (USER_ID, PRODUCT_ID) NOT ENFORCED
) WITH (
  'bucket' = '4',
  'changelog-producer' = 'full-compaction',
  'compaction.interval' = '30min'
);

CREATE TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` (
  `PARTITION_ID` VARCHAR(2147483647),
  `USER_ID` VARCHAR(2147483647) NOT NULL,
  `PRODUCT_MODE` VARCHAR(2147483647),
  `PRODUCT_ID` VARCHAR(2147483647) NOT NULL,
  `BRAND_CODE` VARCHAR(2147483647),
  `START_DATE` VARCHAR(2147483647) NOT NULL,
  `END_DATE` VARCHAR(2147483647),
  `ITEM_ID` VARCHAR(2147483647),
  `USER_ID_A` VARCHAR(2147483647),
  `PROD_ITEM_ID` VARCHAR(2147483647),
  `PRIMARY_USER_ID` VARCHAR(2147483647),
  `UPDATE_TIME` VARCHAR(2147483647),
  `EPARCHY_CODE` VARCHAR(2147483647),
  `PROVINCE_CODE` VARCHAR(2147483647),
  `opt` VARCHAR(2147483647),
  `opttime` VARCHAR(2147483647),
  `in_time` VARCHAR(2147483647),
  `datasource` VARCHAR(2147483647),
  `cdhtime` VARCHAR(2147483647),
  `database_tag` VARCHAR(2147483647),
  `kafka_in_time` TIMESTAMP(3),
  `kafka_out_time` TIMESTAMP(3),
  `paimon_time` TIMESTAMP(3),
  `headers` MAP<VARCHAR(2147483647), VARBINARY(2147483647)>,
  CONSTRAINT `PK_USER_ID_PRODUCT_ID_START_DATE` PRIMARY KEY (`USER_ID`, `PRODUCT_ID`, `START_DATE`) NOT ENFORCED
) WITH (
  'num-sorted-run.stop-trigger' = '2147483647',
  'write-only' = 'true',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'sort-spill-threshold' = '10',
  'bucket' = '256',
  'path' = 'hdfs:/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/ods_r_paimon_tf_f_user_product',
  'snapshot.expire.execution-mode' = 'async',
  'log.scan.remove-normalize' = 'true',
  'snapshot.time-retained' = '6h',
  'file.format' = 'avro',
  'tag.num-retained-max' = '90',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h'
);


CREATE TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product_item` (
  `PRODUCT_ID` VARCHAR(2147483647) NOT NULL,
  `ATTR_CODE` VARCHAR(2147483647) NOT NULL,
  `ATTR_VALUE` VARCHAR(2147483647) NOT NULL,
  `AREA_CODE` VARCHAR(2147483647) NOT NULL,
  `UPDATE_STAFF_ID` VARCHAR(2147483647),
  `UPDATE_DEPART_ID` VARCHAR(2147483647),
  `UPDATE_TIME` VARCHAR(2147483647),
  `opt` VARCHAR(2147483647),
  `opttime` VARCHAR(2147483647),
  `cdhtime` VARCHAR(2147483647),
  `kafka_in_time` TIMESTAMP(3),
  `kafka_out_time` TIMESTAMP(3),
  `paimon_time` TIMESTAMP(3),
  `headers` MAP<VARCHAR(2147483647), VARBINARY(2147483647)>,
  CONSTRAINT `PK_PRODUCT_ID_ATTR_CODE_ATTR_VALUE_AREA_CODE` PRIMARY KEY (`PRODUCT_ID`, `ATTR_CODE`, `ATTR_VALUE`, `AREA_CODE`) NOT ENFORCED
) WITH (
  'bucket' = '1',
  'num-sorted-run.stop-trigger' = '2147483647',
  'path' = 'hdfs:/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/ods_r_paimon_td_b_product_item',
  'log.scan.remove-normalize' = 'true',
  'snapshot.time-retained' = '6h',
  'tag.automatic-creation' = 'process-time',
  'tag.creation-period' = 'daily',
  'tag.num-retained-max' = '90',
  'sort-spill-threshold' = '10',
  'consumer.expiration-time' = '72h'
);

CREATE TABLE `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product` (
  `PRODUCT_ID` VARCHAR(2147483647) NOT NULL,
  `PRODUCT_NAME` VARCHAR(2147483647),
  `PRODUCT_EXPLAIN` VARCHAR(2147483647),
  `PRODUCT_MODE` VARCHAR(2147483647),
  `NET_TYPE_CODE` VARCHAR(2147483647),
  `BRAND_CODE` VARCHAR(2147483647),
  `GROUP_BRAND_CODE` VARCHAR(2147483647),
  `SERVICE_ID` VARCHAR(2147483647),
  `PRODUCT_OBJ_TYPE` VARCHAR(2147483647),
  `RES_TYPE_CODE` VARCHAR(2147483647),
  `DECLARED_PRODUCT_ID` VARCHAR(2147483647),
  `COMP_TAG` VARCHAR(2147483647),
  `ENABLE_TAG` VARCHAR(2147483647),
  `START_ABSOLUTE_DATE` VARCHAR(2147483647),
  `START_OFFSET` VARCHAR(2147483647),
  `START_UNIT` VARCHAR(2147483647),
  `END_ENABLE_TAG` VARCHAR(2147483647),
  `END_ABSOLUTE_DATE` VARCHAR(2147483647),
  `END_OFFSET` VARCHAR(2147483647),
  `END_UNIT` VARCHAR(2147483647),
  `START_DATE` VARCHAR(2147483647),
  `END_DATE` VARCHAR(2147483647),
  `MIN_NUMBER` VARCHAR(2147483647),
  `MAX_NUMBER` VARCHAR(2147483647),
  `CREATE_DATE` VARCHAR(2147483647),
  `VERSION` VARCHAR(2147483647),
  `PRODUCT_STATE` VARCHAR(2147483647),
  `UPDATE_STAFF_ID` VARCHAR(2147483647),
  `UPDATE_DEPART_ID` VARCHAR(2147483647),
  `UPDATE_TIME` VARCHAR(2147483647),
  `PREPAY_TAG` VARCHAR(2147483647),
  `NEED_EXP` VARCHAR(2147483647),
  `PRODUCT_APP_TYPE` VARCHAR(2147483647),
  `PROD_ID` VARCHAR(2147483647),
  `RSRV_VALUE1` VARCHAR(2147483647),
  `RSRV_VALUE2` VARCHAR(2147483647),
  `RSRV_VALUE3` VARCHAR(2147483647),
  `RSRV_VALUE4` VARCHAR(2147483647),
  `RSRV_VALUE5` VARCHAR(2147483647),
  `PROVINCE_CODE` VARCHAR(2147483647),
  `OPT` VARCHAR(2147483647),
  `OPTTIME` VARCHAR(2147483647),
  `CDHTIME` VARCHAR(2147483647),
  `EVENT_TIME` TIMESTAMP(3) NOT NULL,
  `KAFKA_TIME` TIMESTAMP(3) NOT NULL,
  `PAIMON_TIME` TIMESTAMP(3) NOT NULL,
  `HEADERS` MAP<VARCHAR(2147483647), VARBINARY(2147483647)>,
  CONSTRAINT `PK_PRODUCT_ID` PRIMARY KEY (`PRODUCT_ID`) NOT ENFORCED
) WITH (
  'bucket' = '1',
  'path' = 'hdfs:/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/ods_r_paimon_td_b_product',
  'snapshot.time-retained' = '3h',
  'write-only' = 'true',
  'bucket-key' = 'PRODUCT_ID',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h',
  'sort-spill-threshold' = '10'
);

-- 核心处理逻辑优化
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`DM_T_USER_BOARD_ACT_INFO`
WITH MID_03 AS (
  SELECT 
    PRODUCT_ID, 
    ATTR_CODE
  FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product_item`
  WHERE ATTR_CODE IN (
    'PRODUCT_01_TYPE','prod_WXXYF','ecoupon_fee','WPOSZDFQ',
    'zytmisposdzq','ZQFQTOTALM','JR_GD_bigcoupon','JR_Loose_Coupling'
  )
  GROUP BY PRODUCT_ID, ATTR_CODE
),
MID_04 AS (
  SELECT 
    CAST(USER_ID AS STRING) AS USER_ID,
    PRODUCT_ID,
    PRODUCT_MODE,
    BRAND_CODE,
    -- 统一转换为TIMESTAMP_LTZ(3)
    CAST(TO_TIMESTAMP(REGEXP_REPLACE(START_DATE, '[/-]', '-'), 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) AS START_DATE,
    CAST(TO_TIMESTAMP(REGEXP_REPLACE(END_DATE, '[/-]', '-'), 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) AS END_DATE,
    ITEM_ID,
    ROW_NUMBER() OVER(
      PARTITION BY USER_ID, PRODUCT_ID 
      ORDER BY START_DATE DESC
    ) AS rn
  FROM (
    SELECT 
      USER_ID, PRODUCT_ID, PRODUCT_MODE, BRAND_CODE,
      START_DATE, END_DATE, ITEM_ID
    FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product`
    WHERE END_DATE IS NOT NULL
      AND CAST(TO_TIMESTAMP(REGEXP_REPLACE(END_DATE, '[/-]', '-'), 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) 
        BETWEEN CURRENT_TIMESTAMP - INTERVAL '30' DAY AND CURRENT_TIMESTAMP
    UNION ALL
    SELECT 
      USER_ID, PRODUCT_ID, PRODUCT_MODE, BRAND_CODE,
      START_DATE, END_DATE, ITEM_ID
    FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product`
    WHERE START_DATE IS NOT NULL
      AND CAST(TO_TIMESTAMP(REGEXP_REPLACE(START_DATE, '[/-]', '-'), 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) 
        BETWEEN CURRENT_TIMESTAMP - INTERVAL '30' DAY AND CURRENT_TIMESTAMP
  )
  WHERE END_DATE IS NOT NULL
    AND CAST(TO_TIMESTAMP(REGEXP_REPLACE(END_DATE, '[/-]', '-'), 'yyyy-MM-dd HH:mm:ss') AS TIMESTAMP_LTZ(3)) 
      > CURRENT_TIMESTAMP - INTERVAL '30' DAY
),
MID_02 AS (
  SELECT 
    USER_ID,
    PRODUCT_ID,
    PRODUCT_MODE,
    START_DATE,
    END_DATE,
    ITEM_ID
  FROM MID_04
  WHERE rn = 1
    AND PRODUCT_MODE = '50'
  
  UNION ALL
  
  SELECT 
    A.USER_ID,
    A.PRODUCT_ID,
    A.PRODUCT_MODE,
    A.START_DATE,
    A.END_DATE,
    A.ITEM_ID
  FROM (
    SELECT 
      USER_ID,
      PRODUCT_ID,
      PRODUCT_MODE,
      START_DATE,
      END_DATE,
      ITEM_ID
    FROM MID_04
    WHERE rn = 1
      AND PRODUCT_MODE <> '50'
  ) A
  JOIN MID_03 B 
    ON A.PRODUCT_ID = B.PRODUCT_ID
)
SELECT 
  A.USER_ID,
  A.PRODUCT_ID,
  B.PRODUCT_NAME,
  CASE 
    WHEN (A.PRODUCT_MODE = '50' OR AA.PRODUCT_ID IS NOT NULL) THEN '分期与传统合约'
    WHEN B.PRODUCT_NAME LIKE '%折%' THEN '折扣合约'
    ELSE '普通轻合约'
  END AS ACT_TYPE,
  CAST(A.START_DATE AS TIMESTAMP(3)) AS START_DATE,
  CAST(A.END_DATE AS TIMESTAMP(3)) AS END_DATE,
  A.ITEM_ID,
  CAST(CURRENT_TIMESTAMP AS TIMESTAMP(3)) AS UPDATE_TIME,
  CAST('合约' AS STRING) AS DATA_TYPE,
  B.PRODUCT_EXPLAIN,
  CAST(B.RSRV_VALUE2 AS STRING) AS M_RENT_FEE
FROM MID_02 A
JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product` B 
  ON A.PRODUCT_ID = B.PRODUCT_ID
LEFT JOIN (SELECT DISTINCT PRODUCT_ID FROM MID_03) AA 
  ON A.PRODUCT_ID = AA.PRODUCT_ID
WHERE 
  A.START_DATE IS NOT NULL
  AND A.END_DATE IS NOT NULL;