import org.apache.kafka.clients.admin.*;
import org.apache.kafka.common.config.ConfigResource;
import org.apache.kafka.common.errors.UnknownTopicOrPartitionException;
import org.apache.kafka.clients.admin.Config;
import org.apache.kafka.common.config.ConfigEntry;

import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.Map;
import java.util.Collection;

public class KafkaConsumerChangeTopicTime {

    public static void main(String[] args) {
        // Kafka Broker 地址（逗号分隔多个地址）
        // 目标 Topic 名称
        String topicName = "IN_KAFKA_TO_HBASE";
        String userName = "context";
        String pwd = "context";
        String bootstrapServers = "10.177.18.44:9092,10.177.18.45:9092,10.177.18.46:9092,10.177.18.47:9092,10.177.18.48:9092,10.177.18.49:9092,10.177.18.50:9092,10.177.18.51:9092,10.177.18.52:9092,10.177.18.53:9092,10.177.25.76:9092";
        String config = "org.apache.kafka.common.security.plain.PlainLoginModule";
        String protocol = "SASL_PLAINTEXT";
        String mechanism = "PLAIN";
        String group = "exclusion";
        String offset = "earliest";
        // 留存时间（单位：毫秒）
        long retentionMs = 604800000;  // 3天 = 3*24*60*60*1000

        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("group.id", group);
        props.put("enable.auto.commit", "true");
        props.put("auto.commit.interval.ms", "1000");
        props.put("auto.offset.reset", offset);
        props.put("session.timeout.ms", "50000");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        if (userName.length() != 0 && !userName.equals("")) {
            //用户名密码方式 begin
            props.put("sasl.jaas.config", config + " required username=\"" + userName + "\" password=\"" + pwd + "\";");
            props.put("security.protocol", protocol);
            props.put("sasl.mechanism", mechanism);
            //用户名密码方式 end
        }

        // 1. 创建 AdminClient 配置
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);

        try (AdminClient adminClient = AdminClient.create(props)) {
            ConfigResource configResource = new ConfigResource(
                    ConfigResource.Type.TOPIC,
                    topicName
            );

            // 使用完整的 alterConfigs API
            Config retentionConfig = new Config(Collections.singleton(
                    new ConfigEntry("retention.ms", String.valueOf(retentionMs))
            ));

            Map<ConfigResource, Config> configMap = Collections.singletonMap(
                    configResource,
                    retentionConfig
            );

            AlterConfigsResult alterResult = adminClient.alterConfigs(configMap);
            alterResult.all().get();
            System.out.println("Topic 留存时间已修改为 " + retentionMs + " 毫秒");
        } catch (ExecutionException e) {
            if (e.getCause() instanceof UnknownTopicOrPartitionException) {
                System.err.println("Topic 不存在: " + topicName);
            } else {
                e.printStackTrace();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("操作被中断");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}