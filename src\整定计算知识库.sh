#!/bin/bash

# 参数设置
CF_BASE="com1"  # 列簇基本名称(不含限定符)
BATCH_SIZE=1000  # 每批删除的行数

HBASE_HOME="/usr/hdp/current/hbase-client"
HBASE_USER="hbase"
TABLE_NAME="ctg810544697531_hh_fed_sub23_lclabel:label_lc_realtime_data_test"
# 要删除的列簇，多个列簇用逗号分隔
COLUMN_FAMILY="com1:IS_CONTRACT_USER"

# ZooKeeper配置
ZK_HOSTS="************,************,************"
ZK_PORT="2181"
ZNODE_ROOT="/hbase-unsecure"

# 日志功能
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log "开始处理: 表 ${TABLE_NAME}, 列簇 ${COLUMN_FAMILY}"

# 创建查询脚本
QUERY_SCRIPT="/tmp/hbase_query_$(date +\%s).txt"
cat > ${QUERY_SCRIPT} << EOF
# 测试不同的列簇访问方式
# 方法1: 完整列簇名
scan '${TABLE_NAME}', {COLUMNS => '${COLUMN_FAMILY}', LIMIT => 10}
# 方法2: 基本列簇名
scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}', LIMIT => 10}
# 方法3: 数组语法
scan '${TABLE_NAME}', {COLUMNS => ['${COLUMN_FAMILY}'], LIMIT => 10}
EOF

# 执行查询检查哪种方式可以访问数据
log "测试数据访问方式..."
QUERY_OUTPUT=$(${HBASE_HOME}/bin/hbase shell ${QUERY_SCRIPT} 2>&1)

# 判断哪种访问方式可以获取数据
METHOD1=$(echo "$QUERY_OUTPUT" | grep -A10 "方法1:" | grep -c "row=")
METHOD2=$(echo "$QUERY_OUTPUT" | grep -A10 "方法2:" | grep -c "row=")
METHOD3=$(echo "$QUERY_OUTPUT" | grep -A10 "方法3:" | grep -c "row=")

# 选择最佳方法
SCAN_METHOD=""
if [ $METHOD1 -gt 0 ]; then
    SCAN_METHOD="scan '${TABLE_NAME}', {COLUMNS => '${COLUMN_FAMILY}'}"
    log "使用方法1: 完整列簇名"
elif [ $METHOD3 -gt 0 ]; then
    SCAN_METHOD="scan '${TABLE_NAME}', {COLUMNS => ['${COLUMN_FAMILY}']}"
    log "使用方法3: 数组语法"
elif [ $METHOD2 -gt 0 ]; then
    SCAN_METHOD="scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}'}"
    log "使用方法2: 基本列簇名"
else
    SCAN_METHOD="scan '${TABLE_NAME}', {COLUMNS => '${CF_BASE}'}"
    log "无法确定最佳方法，使用默认方法"
fi

# 扫描全表获取rowkeys
SCAN_SCRIPT="/tmp/hbase_scan_$(date +\%s).txt"
cat > ${SCAN_SCRIPT} << EOF
${SCAN_METHOD}
EOF

log "扫描表获取rowkey列表..."
SCAN_OUTPUT=$(${HBASE_HOME}/bin/hbase shell ${SCAN_SCRIPT} 2>&1)
ROWKEYS=$(echo "$SCAN_OUTPUT" | grep -E "row=|rowkey=" | awk -F"=" '{print $2}' | awk -F"," '{print $1}' | tr -d "'" | tr -d '"' | sort | uniq)
ROWKEY_COUNT=$(echo "$ROWKEYS" | grep -v "^$" | wc -l)

log "找到 ${ROWKEY_COUNT} 行数据需要删除"

if [ $ROWKEY_COUNT -eq 0 ]; then
    log "没有找到数据，退出执行"
    exit 0
fi

# 创建删除脚本
DELETE_SCRIPT="/tmp/hbase_delete_$(date +\%s).txt"
cat > ${DELETE_SCRIPT} << EOF
# 引入Java类
import org.apache.hadoop.hbase.util.Bytes

# 初始化批量删除数组
deleteall_batch = []
EOF

# 分批处理rowkeys
CURRENT_COUNT=0
BATCH_COUNT=0

echo "$ROWKEYS" | while read -r rowkey; do
    # 跳过空行
    if [ -z "$rowkey" ]; then
        continue
    fi
    
    # 添加到脚本
    echo "# 处理rowkey: $rowkey" >> ${DELETE_SCRIPT}
    echo "delete = org.apache.hadoop.hbase.client.Delete.new('$rowkey')" >> ${DELETE_SCRIPT}
    echo "delete.addFamily(Bytes.toBytes('${CF_BASE}'))" >> ${DELETE_SCRIPT}
    echo "deleteall_batch.push(delete)" >> ${DELETE_SCRIPT}
    
    CURRENT_COUNT=$((CURRENT_COUNT + 1))
    
    # 每达到批处理大小就提交一次
    if [ $((CURRENT_COUNT % BATCH_SIZE)) -eq 0 ]; then
        BATCH_COUNT=$((BATCH_COUNT + 1))
        echo "# 提交批次 $BATCH_COUNT ($CURRENT_COUNT 行)" >> ${DELETE_SCRIPT}
        echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
        echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
        echo "puts \"已删除 ${CURRENT_COUNT} 行...\"" >> ${DELETE_SCRIPT}
        echo "deleteall_batch = []" >> ${DELETE_SCRIPT}
    fi
done

# 处理最后一批
if [ $((CURRENT_COUNT % BATCH_SIZE)) -ne 0 ]; then
    BATCH_COUNT=$((BATCH_COUNT + 1))
    echo "# 提交最后一批 $BATCH_COUNT" >> ${DELETE_SCRIPT}
    echo "table = get_table('${TABLE_NAME}')" >> ${DELETE_SCRIPT}
    echo "table.delete(deleteall_batch)" >> ${DELETE_SCRIPT}
fi

# 添加退出命令
echo "puts \"总共删除 ${CURRENT_COUNT} 行数据完成\"" >> ${DELETE_SCRIPT}
echo "exit" >> ${DELETE_SCRIPT}

# 执行删除
log "执行删除操作..."
${HBASE_HOME}/bin/hbase shell ${DELETE_SCRIPT}

# 清理临时文件
rm -f ${QUERY_SCRIPT} ${SCAN_SCRIPT} ${DELETE_SCRIPT}

log "操作完成"