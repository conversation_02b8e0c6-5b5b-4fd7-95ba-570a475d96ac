CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;

SET 'sql-client.execution.result-mode' = 'tableau';
SET 'table.exec.sink.upsert-materialize'='NONE';
SET 'table.exec.sink.not-null-enforcer'='DROP';

CREATE FUNCTION IF NOT EXISTS COP_DTS_MSG AS 'com.chinaunicom.rts.cop.udf.DtsMsgFunction';

--kafka表
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_tf_order_driver_xkf_call_info` (
  RAW_FIELD STRING,
  DATA_RELEASE_TIME as CAST(PROCTIME() as TIMESTAMP(3)),
  DATA_RECEIVE_TIME TIMESTAMP(3) METADATA FROM 'timestamp',
  PROCTIME as PROCTIME() ,
  HEADERS MAP < STRING, BYTES > METADATA FROM 'headers'
) WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '10.177.81.172:9667,10.177.81.173:9667,10.177.81.174:9667,10.177.81.175:9667,10.177.81.176:9667,10.177.81.177:9667,10.177.81.178:9667,10.177.81.179:9667,10.177.81.180:9667,10.177.81.181:9667,10.177.81.182:9667,10.177.81.183:9667,10.177.81.184:9667,10.177.81.185:9667,10.177.81.186:9667,10.177.81.187:9667,10.177.81.188:9667,10.177.81.189:9667,10.177.81.190:9667,10.177.81.191:9667,10.177.81.192:9667',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="cjzh_gch_pt" password="it6GjDoJyd";',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.security.protocol' = 'SASL_PLAINTEXT',
  'topic' = 'CUSTOMER_OPERATION_34',
  'scan.topic-partition-discovery.interval' = '30000',
  'scan.startup.mode' = 'earliest-offset',
  'properties.session.timeout.ms' = '300000',
  'format' = 'raw'
);

--paimon表-外呼数据
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_order_driver_xkf_call_info` (
  INFO_ID STRING,
  CITY_CODE STRING,
  RECORD_FILE_NAME STRING,
  START_TIME STRING,
  CALL_ID STRING,
  SINGLE_CALL STRING,
  CALL_RESULT STRING,
  CALLER_SERIAL_NUMBER STRING,
  CALL_STATUS STRING,
  USER_ID STRING,
  BUSINESS_BELONG_CODE STRING,
  UPDATE_TIME STRING,
  PROVINCE_CODE STRING,
  PROVINCE_NAME STRING,
  RECORD_FILE_DIR STRING,
  CALLED_SERIAL_NUMBER STRING,
  STRATEGY_ID STRING,
  PK_ID STRING,
  CALL_TYPE STRING,
  CREATE_TIME STRING,
  DURATION STRING,
  TASK_ID STRING,
  QUALITY_STATUS STRING,
  opt STRING,
  opttime STRING,
  kafka_in_time TIMESTAMP(3),
  paimon_time TIMESTAMP(3),
  CONSTRAINT `INFO_ID` PRIMARY KEY (INFO_ID) NOT ENFORCED
) WITH (
  'bucket' = '32',
  'bucket-key' = 'INFO_ID',
  'num-sorted-run.stop-trigger' = '2147483647',
  'snapshot.expire.execution-mode' = 'async',
  'log.scan.remove-normalize' = 'true',
  'snapshot.time-retained' = '12h',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h',
  'sort-spill-threshold' = '10'
);

--入湖
insert into
  `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_order_driver_xkf_call_info`
select
  COALESCE(NULLIF(TRIM(COP_DTS_MSG(RAW_FIELD, 'INFO_ID')), ''), NULLIF(TRIM(JSON_VALUE(RAW_FIELD, '$[0].key_column[0].INFO_ID')), '')) AS INFO_ID,
  ( COP_DTS_MSG(RAW_FIELD,'CITY_CODE') ) CITY_CODE,
  ( COP_DTS_MSG(RAW_FIELD,'RECORD_FILE_NAME') ) RECORD_FILE_NAME,
  ( COP_DTS_MSG(RAW_FIELD,'START_TIME') ) START_TIME,
  ( COP_DTS_MSG(RAW_FIELD,'CALL_ID') ) CALL_ID,
  ( COP_DTS_MSG(RAW_FIELD,'SINGLE_CALL') ) SINGLE_CALL,
  ( COP_DTS_MSG(RAW_FIELD,'CALL_RESULT') ) CALL_RESULT,
  ( COP_DTS_MSG(RAW_FIELD,'CALLER_SERIAL_NUMBER') ) CALLER_SERIAL_NUMBER,
  ( COP_DTS_MSG(RAW_FIELD,'CALL_STATUS') ) CALL_STATUS,
  ( COP_DTS_MSG(RAW_FIELD,'USER_ID') ) USER_ID,
  ( COP_DTS_MSG(RAW_FIELD,'BUSINESS_BELONG_CODE') ) BUSINESS_BELONG_CODE,
  ( COP_DTS_MSG(RAW_FIELD,'UPDATE_TIME') ) UPDATE_TIME,
  ( COP_DTS_MSG(RAW_FIELD,'PROVINCE_CODE') ) PROVINCE_CODE,
  CASE ( COP_DTS_MSG(RAW_FIELD,'PROVINCE_CODE') )
    WHEN '10' THEN '内蒙古'
    WHEN '11' THEN '北京'
    WHEN '13' THEN '天津'
    WHEN '17' THEN '山东'
    WHEN '18' THEN '河北'
    WHEN '19' THEN '山西'
    WHEN '30' THEN '安徽'
    WHEN '31' THEN '上海'
    WHEN '34' THEN '江苏'
    WHEN '36' THEN '浙江'
    WHEN '38' THEN '福建'
    WHEN '50' THEN '海南'
    WHEN '51' THEN '广东'
    WHEN '59' THEN '广西'
    WHEN '70' THEN '青海'
    WHEN '71' THEN '湖北'
    WHEN '74' THEN '湖南'
    WHEN '75' THEN '江西'
    WHEN '76' THEN '河南'
    WHEN '79' THEN '西藏'
    WHEN '81' THEN '四川'
    WHEN '83' THEN '重庆'
    WHEN '84' THEN '陕西'
    WHEN '85' THEN '贵州'
    WHEN '86' THEN '云南'
    WHEN '87' THEN '甘肃'
    WHEN '88' THEN '宁夏'
    WHEN '89' THEN '新疆'
    WHEN '90' THEN '吉林'
    WHEN '91' THEN '辽宁'
    WHEN '97' THEN '黑龙江'
    ELSE ''
    END as PROVINCE_NAME,
  ( COP_DTS_MSG(RAW_FIELD,'RECORD_FILE_DIR') ) RECORD_FILE_DIR,
  ( COP_DTS_MSG(RAW_FIELD,'CALLED_SERIAL_NUMBER') ) CALLED_SERIAL_NUMBER,
  ( COP_DTS_MSG(RAW_FIELD,'STRATEGY_ID') ) STRATEGY_ID,
  ( COP_DTS_MSG(RAW_FIELD,'PK_ID') ) PK_ID,
  ( COP_DTS_MSG(RAW_FIELD,'CALL_TYPE') ) CALL_TYPE,
  ( COP_DTS_MSG(RAW_FIELD,'CREATE_TIME') ) CREATE_TIME,
  ( COP_DTS_MSG(RAW_FIELD,'DURATION') ) DURATION,
  ( COP_DTS_MSG(RAW_FIELD,'TASK_ID') ) TASK_ID,
  ( COP_DTS_MSG(RAW_FIELD,'QUALITY_STATUS') ) QUALITY_STATUS,
  ( COP_DTS_MSG(RAW_FIELD,'operate') ) OPT,
  ( COP_DTS_MSG(RAW_FIELD,'operate_time') ) OPTTIME,
  DATA_RECEIVE_TIME kafka_in_time,
  CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS PAIMON_TIME
from
  `hive_catalog`.`ubd_sscj_prod_flink`.`ods_r_kafka_tf_order_driver_xkf_call_info`/*+ OPTIONS('properties.group.id'='rtag-tf_order_driver_xkf_call_info','scan.startup.mode'='group-offsets','properties.enable.auto.commit'='true','properties.auto.offset.reset'='earliest','properties.auto.commit.interval.ms'='1000')*/
;
