account_id|phone_number|coupon_id|province_code|city_code|merchant_id|create_time|coupon_no|start_date|end_date|consume_time|status|coupon_img|last_coupon_kind|first_coupon_kind|act_id|goods_id|consume_way|cat_name|channel_id|source|id|delay_status|trade_id|use_end_date|cost|wx_coupon_id|wx_code|activation_time|amount|sale_price|settlement_price|coupon_name|merchant_name|channel_name|version_number|order_no|remark
读取原值:****************|***********|****************|30|0555|****************|2025-03-01 00:00:00.0||2025-03-01 00:00:02.0|2025-04-01 23:59:59.0||0||*********|********|********00307025|********00242816|2722||297196|联通PLUS会员—领VIP会员（白银）|****************||****************||1620||||0||0|腾讯视频VIP会员-月卡|联通在线信息科技有限公司|手厅-九宫格|6|1200000017773861588|
生成最终string:[{"column":[{"account_id":"****************"},{"phone_number":"***********"},{"coupon_id":"****************"},{"province_code":"30"},{"city_code":"0555"},{"merchant_id":"****************"},{"create_time":"2025-03-01 00:00:00.0"},{"coupon_no":""},{"start_date":"2025-03-01 00:00:02.0"},{"end_date":"2025-04-01 23:59:59.0"},{"consume_time":""},{"status":"0"},{"coupon_img":""},{"last_coupon_kind":"*********"},{"first_coupon_kind":"********"},{"act_id":"********00307025"},{"goods_id":"********00242816"},{"consume_way":"2722"},{"cat_name":""},{"channel_id":"297196"},{"source":"联通PLUS会员—领VIP会员（白银）"},{"delay_status":""},{"trade_id":"****************"},{"use_end_date":""},{"cost":"1620"},{"wx_coupon_id":""},{"wx_code":""},{"activation_time":""},{"amount":"0"},{"sale_price":""},{"settlement_price":"0"},{"coupon_name":"腾讯视频VIP会员-月卡"},{"merchant_name":"联通在线信息科技有限公司"},{"channel_name":"手厅-九宫格"},{"version_number":"6"},{"order_no":"1200000017773861588"},{"remark":""}],"db_name":"prodrai_account_ytpl_0015","key_column":[{"id":"****************"}],"operate":"INSERT","operate_time":"**********","source":"prodrai_account_ytpl_0015","source_type":"mysql+DTS","table_name":"tf_acc_coupon","trail_rba":"*********@0","trail_seq":"0"}]

#!/bin/bash

# 检查输入文件是否存在
if [ $# -ne 1 ]; then
    echo "Usage: $0 <input_file>"
    exit 1
fi

input_file=$1
output_file="${input_file%.*}_converted.txt"

# 使用AWK进行高性能处理
LC_ALL=C awk -F '|' '
BEGIN {
    OFS = ""
    ORS = "\n"
    # 预定义固定字段
    fixed_fields = ",\"db_name\":\"prodrai_account_ytpl_0015\",\"operate\":\"INSERT\",\"operate_time\":\"**********\",\"source\":\"prodrai_account_ytpl_0015\",\"source_type\":\"mysql+DTS\",\"table_name\":\"tf_acc_coupon\",\"trail_rba\":\"*********@0\",\"trail_seq\":\"0\""
}

NR == 1 {
    # 读取表头并过滤换行
    for (i=1; i<=NF; i++) {
        headers[i] = $i
        gsub(/[\n\r]/, "", headers[i])  # 新增表头换行过滤
    }
    next
}

{
    # 跳过空行
    if (NF < 38) next
    
    # 获取ID（第22字段）
    id = $22
    gsub(/[[:space:]\n\r]/, "", id)
    
    # 构建column数组
    json = "[{\"column\":["
    for (i=1; i<=NF; i++) {
        if (i == 22) continue
        
        # 字段净化处理（新增对remark字段的特别处理）
        gsub(/[\n\r\t]/, " ", $i)
        gsub(/"/, "\\\"", $i)
        gsub(/\\/, "\\\\", $i)
        
        # 如果是remark字段（第38字段）需要额外处理
        if (i == 38) {
            gsub(/\n/, "", $i)  # 强制移除remark字段的换行
        }
        
        json = json (i>1 && i!=22 ? "," : "") "{\"" headers[i] "\":\"" $i "\"}"
    }
    
    # 拼接完整JSON
    print id "|" json "],\"key_column\":[{\"id\":\"" id "\"}]" fixed_fields "}]"
}' "$input_file" > "$output_file"

echo "转换完成！输出文件：$output_file"