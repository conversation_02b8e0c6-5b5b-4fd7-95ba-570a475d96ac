SELECT
    DATE_FORMAT(paimon_ingest_time, 'yyyy-MM-dd') AS ingest_date,
    table_source,
    COUNT(*) AS source_count
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_zaixian_app_orderInfo`
WHERE 
    paimon_ingest_time >= CAST(
        CONCAT(
            DATE_FORMAT(CURRENT_TIMESTAMP, 'yyyy-MM'),
            '-01 00:00:00'
        ) AS TIMESTAMP(3)
    )
    AND paimon_ingest_time < CAST(
        CONCAT(
            DATE_FORMAT(
                TIMESTAMPADD(MONTH, 1, CURRENT_TIMESTAMP), 
                'yyyy-MM'
            ),
            '-01 00:00:00'
        ) AS TIMESTAMP(3)
    )
GROUP BY 
    DATE_FORMAT(paimon_ingest_time, 'yyyy-MM-dd'),
    table_source
ORDER BY 
    DATE_FORMAT(paimon_ingest_time, 'yyyy-MM-dd'), 
    table_source;