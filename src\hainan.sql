-- 创建Paimon Catalog
CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
-- CREATE CATALOG hive_catalog WITH (
--   'type' = 'hive',
--   'default-database' = 'ubd_sscj_prod_flink',
--   'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
-- );
-- USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';

  
-- 创建目标Paimon表
CREATE TABLE  IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dm_t_user_board_act_info` (
  `USER_ID` STRING NOT NULL,
  `PRODUCT_ID` STRING NOT NULL,
  `PRODUCT_NAME` STRING,
  `ACT_TYPE` STRING,
  `START_DATE` STRING,
  `END_DATE` STRING,
  `ITEM_ID` STRING,
  `UPDATE_TIME` TIMESTAMP(3),
  `DATA_TYPE` STRING,
  `PRODUCT_EXPLAIN` STRING,
  `M_RENT_FEE` STRING,
  CONSTRAINT `PK_USER_PRODUCT` PRIMARY KEY (`USER_ID`, `PRODUCT_ID`) NOT ENFORCED
) WITH (
  'bucket' = '64',
  'path' = 'hdfs:/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/dm_t_user_board_act_info',
  'snapshot.time-retained' = '6h',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h'
);


-- 1. 创建临时视图 MID_T_USER_BOARD_ACT_INFO_03
CREATE TEMPORARY VIEW MID_T_USER_BOARD_ACT_INFO_03 AS
SELECT PRODUCT_ID, ATTR_CODE
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product_item`;

-- 2. 创建临时视图 MID_T_USER_BOARD_ACT_INFO_04
CREATE TEMPORARY VIEW MID_T_USER_BOARD_ACT_INFO_04 AS
SELECT PARTITION_ID, USER_ID, PRODUCT_MODE, PRODUCT_ID, BRAND_CODE, 
       START_DATE, END_DATE, ITEM_ID, USER_ID_A
FROM (
  SELECT *, 
         ROW_NUMBER() OVER(PARTITION BY USER_ID, PRODUCT_ID ORDER BY START_DATE DESC) AS RN
  FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product`
  WHERE END_DATE BETWEEN CAST(TIMESTAMPADD(DAY, -30, CURRENT_TIMESTAMP) AS STRING) AND CAST(CURRENT_TIMESTAMP AS STRING)
     OR START_DATE BETWEEN CAST(TIMESTAMPADD(DAY, -30, CURRENT_TIMESTAMP) AS STRING) AND CAST(CURRENT_TIMESTAMP AS STRING)
) WHERE RN = 1;

-- 3. 创建临时视图 MID_T_USER_BOARD_ACT_INFO_02
CREATE TEMPORARY VIEW MID_T_USER_BOARD_ACT_INFO_02 AS
SELECT USER_ID, PRODUCT_ID, PRODUCT_MODE, START_DATE, END_DATE, ITEM_ID
FROM MID_T_USER_BOARD_ACT_INFO_04
WHERE PRODUCT_MODE = '50'
UNION ALL
SELECT a.USER_ID, a.PRODUCT_ID, a.PRODUCT_MODE, a.START_DATE, a.END_DATE, a.ITEM_ID
FROM MID_T_USER_BOARD_ACT_INFO_04 a
JOIN (
  SELECT DISTINCT PRODUCT_ID
  FROM MID_T_USER_BOARD_ACT_INFO_03
  WHERE ATTR_CODE IN ('PRODUCT_01_TYPE', 'prod_WXXYF', 'ecoupon_fee', 'WPOSZDFQ',
                     'zytmisposdzq', 'ZQFQTOTALM', 'JR_GD_bigcoupon', 'JR_Loose_Coupling')
) b ON a.PRODUCT_ID = b.PRODUCT_ID
WHERE a.PRODUCT_MODE <> '50';

-- 4. 插入最终结果到目标表
INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dm_t_user_board_act_info`
-- 合约类数据
SELECT 
  a.USER_ID,
  a.PRODUCT_ID,
  b.PRODUCT_NAME,
  CASE
    WHEN (a.PRODUCT_MODE = '50' OR aa.PRODUCT_ID IS NOT NULL) THEN '分期与传统合约'
    WHEN b.PRODUCT_NAME LIKE '%折%' THEN '折扣合约'
    ELSE '普通轻合约'
  END AS ACT_TYPE,
  a.START_DATE,
  a.END_DATE,
  a.ITEM_ID,
  CURRENT_TIMESTAMP AS UPDATE_TIME,
  '合约' AS DATA_TYPE,
  b.PRODUCT_EXPLAIN,
  b.RSRV_VALUE2 AS M_RENT_FEE
FROM MID_T_USER_BOARD_ACT_INFO_02 a
JOIN `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product` b ON a.PRODUCT_ID = b.PRODUCT_ID
LEFT JOIN (
  SELECT DISTINCT PRODUCT_ID 
  FROM MID_T_USER_BOARD_ACT_INFO_03
) aa ON a.PRODUCT_ID = aa.PRODUCT_ID

UNION ALL

-- 折扣类数据
SELECT 
  a.USER_ID,
  a.PRODUCT_ID,
  b.PRODUCT_NAME,
  '折扣' AS ACT_TYPE,
  a.START_DATE,
  a.END_DATE,
  a.ITEM_ID,
  CURRENT_TIMESTAMP AS UPDATE_TIME,
  '折扣' AS DATA_TYPE,
  b.PRODUCT_EXPLAIN,
  b.RSRV_VALUE2 AS M_RENT_FEE
FROM MID_T_USER_BOARD_ACT_INFO_04 a
JOIN (
  SELECT PRODUCT_ID, PRODUCT_NAME, PRODUCT_EXPLAIN, RSRV_VALUE2
  FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_td_b_product`
  WHERE PRODUCT_NAME LIKE '%折%'
) b ON a.PRODUCT_ID = b.PRODUCT_ID

UNION ALL

-- 附加类数据
SELECT 
  USER_ID,
  PRODUCT_ID,
  CAST('' AS STRING) AS PRODUCT_NAME,
  '附加' AS ACT_TYPE,
  START_DATE,
  END_DATE,
  ITEM_ID,
  CURRENT_TIMESTAMP AS UPDATE_TIME,
  '附加' AS DATA_TYPE,
  CAST('' AS STRING) AS PRODUCT_EXPLAIN,
  CAST('' AS STRING) AS M_RENT_FEE
FROM MID_T_USER_BOARD_ACT_INFO_04
WHERE PRODUCT_MODE = '01'

UNION ALL

-- 主产品类数据
SELECT 
  USER_ID,
  PRODUCT_ID,
  CAST('' AS STRING) AS PRODUCT_NAME,
  '主产品' AS ACT_TYPE,
  START_DATE,
  END_DATE,
  ITEM_ID,
  CURRENT_TIMESTAMP AS UPDATE_TIME,
  '主产品' AS DATA_TYPE,
  CAST('' AS STRING) AS PRODUCT_EXPLAIN,
  CAST('' AS STRING) AS M_RENT_FEE
FROM MID_T_USER_BOARD_ACT_INFO_04
WHERE PRODUCT_MODE = '00';