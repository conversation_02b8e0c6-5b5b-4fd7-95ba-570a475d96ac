CREATE CATALOG paimon_catalog WITH (
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;

CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;

SET 'sql-client.execution.result-mode' = 'tableau';
SET 'yarn.application.queue' = 'ctg782612549842_hh_fed_sub15_sssc_prod15';
SET 'table.exec.sink.not-null-enforcer' = 'DROP';
SET 'table.exec.sink.upsert-materialize' = 'NONE';

--paimon表-话费券
CREATE TABLE IF NOT EXISTS `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_HeFei_product_info` (
  `USER_ID` STRING NOT NULL,
  `PRODUCT_ID` STRING NOT NULL,
  `START_DATE` STRING,
  `END_DATE` STRING,
  `PAIMON_TIME` TIMESTAMP(3),
  CONSTRAINT `PK_USER_PRODUCT` PRIMARY KEY (`USER_ID`, `PRODUCT_ID`) NOT ENFORCED
) WITH (
  'bucket' = '64',
  'write-buffer-size' = '256mb',
  'write-buffer-spillable' = 'true',
  'num-levels' = '5',
  'snapshot.time-retained' = '6h',
  'file.format' = 'avro',
  'metadata.stats-mode' = 'none',
  'consumer.expiration-time' = '72h'
);

CREATE TEMPORARY VIEW specified_products AS
SELECT 
    '90381980' AS product_id
UNION ALL SELECT '90382029'
UNION ALL SELECT '90382030'
UNION ALL SELECT '90382035'
UNION ALL SELECT '90715165'
UNION ALL SELECT '90723404'
UNION ALL SELECT '90991656'
UNION ALL SELECT '91277029'
UNION ALL SELECT '91277048'
UNION ALL SELECT '91277054'
UNION ALL SELECT '90366811'
UNION ALL SELECT '90366812'
UNION ALL SELECT '90366813'
UNION ALL SELECT '91008468'
UNION ALL SELECT '91081955'
UNION ALL SELECT '91081967'
UNION ALL SELECT '91082147'
UNION ALL SELECT '91082150'
UNION ALL SELECT '91328961'
UNION ALL SELECT '91328962'
UNION ALL SELECT '91328963'
UNION ALL SELECT '91328964'
UNION ALL SELECT '91328967'
UNION ALL SELECT '91328968'
UNION ALL SELECT '91328970'
UNION ALL SELECT '91334665'
UNION ALL SELECT '91334676'
UNION ALL SELECT '91334678'
UNION ALL SELECT '91334680'
UNION ALL SELECT '91334681'
UNION ALL SELECT '91334684'
UNION ALL SELECT '91334685'
UNION ALL SELECT '91334687'
UNION ALL SELECT '91334690'
UNION ALL SELECT '91334691'
UNION ALL SELECT '91334692'
UNION ALL SELECT '90991668'
UNION ALL SELECT '91233779'
UNION ALL SELECT '91295467'
UNION ALL SELECT '91295475'
UNION ALL SELECT '91373798'
UNION ALL SELECT '91258941'
UNION ALL SELECT '91283113'
UNION ALL SELECT '90612474'
UNION ALL SELECT '91233780'
UNION ALL SELECT '91295480'
UNION ALL SELECT '91295482'
UNION ALL SELECT '91319744';

-- 主查询：获取满足条件的用户及其产品信息
WITH user_products AS (
    -- 获取每个用户的所有产品信息
    SELECT 
        u.USER_ID,
        SUM(CASE WHEN EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = u.PRODUCT_ID) THEN 1 ELSE 0 END) AS specified_product_count,
        -- 有效的指定产品数量（END_DATE > now）
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = u.PRODUCT_ID) 
             AND u.END_DATE > FORMAT(CURRENT_DATE, 'yyyyMMdd')
            THEN 1 
            ELSE 0 
        END) AS active_specified_product_count,
        -- 检查是否有指定产品在第二天到期
        SUM(CASE 
            WHEN EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = u.PRODUCT_ID)
             AND u.END_DATE > FORMAT(CURRENT_DATE, 'yyyyMMdd')
             AND TO_DATE(FROM_UNIXTIME(UNIX_TIMESTAMP(u.END_DATE, 'yyyyMMdd'))) = DATE_ADD(CURRENT_DATE, 1)
            THEN 1
            ELSE 0
        END) AS expires_tomorrow_count
    FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` u
    GROUP BY u.USER_ID
)

INSERT INTO `paimon_catalog`.`ubd_sscj_prod_flink`.`dwa_r_paimon_HeFei_product_info`
SELECT 
    up.USER_ID,
    p.PRODUCT_ID,
    p.START_DATE,
    p.END_DATE,
    CAST(CURRENT_TIMESTAMP as TIMESTAMP(3)) AS paimon_time
FROM `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user_product` p
JOIN user_products up ON p.USER_ID = up.USER_ID
WHERE 
    -- 用户只有一个有效的指定产品
    up.active_specified_product_count = 1
    -- 用户总共只有一个指定产品
    AND up.specified_product_count = 1
    -- 该产品明天到期
    AND up.expires_tomorrow_count = 1
    -- 只输出指定产品的记录
    AND EXISTS (SELECT 1 FROM specified_products sp WHERE sp.product_id = p.PRODUCT_ID)
    -- 确认该记录是活跃的
    AND p.END_DATE > FORMAT(CURRENT_DATE, 'yyyyMMdd'); 