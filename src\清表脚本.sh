#!/bin/bash
# 生产环境慎用！建议先在测试环境验证

# 原始路径配置
SOURCE_PATH="hdfs://slfn2/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/ods_r_paimon_tf_f_user_product_test"
# 回收站对应路径
TRASH_PATH="hdfs://slfn2/user/hh_slfn2_sschj/.Trash/Current/user/hh_slfn2_sschj/paimon/ubd_sscj_prod_flink.db/ods_r_paimon_tf_f_user_product_test"

# 安全验证函数
check_hdfs_path() {
    hdfs dfs -test -d $1
    if [ $? -ne 0 ]; then
        return 1
    fi
    return 0
}

# 主循环
while true; do
    # 获取当前目录列表
    dirs=($(hdfs dfs -ls $SOURCE_PATH | grep "^d" | awk '{print $8}' | xargs -n1 basename))
    
    # 退出条件
    if [ ${#dirs[@]} -eq 0 ]; then
        echo "所有目录已清理完成"
        break
    fi
    
    # 批次处理（每次3个）
    echo "当前剩余目录数: ${#dirs[@]}"
    echo "本次处理目录: ${dirs[@]:0:3}"
    
    # 删除操作
    for ((i=0; i<3 && i<${#dirs[@]}; i++)); do
        target="${SOURCE_PATH}/${dirs[$i]}"
        if check_hdfs_path "$target"; then
            echo "正在删除: ${dirs[$i]}"
            hdfs dfs -rm -r "$target" 2>&1 | sed 's/^/  >> /'
        else
            echo "目录不存在: ${dirs[$i]}"
        fi
    done
    
    # 第一阶段等待
    echo "等待5分钟（原始目录删除完成）..."
    sleep 300

    # 清理回收站
    if check_hdfs_path "$TRASH_PATH"; then
        echo "清理回收站..."
        hdfs dfs -rm -r -skipTrash "$TRASH_PATH/*" 2>&1 | sed 's/^/  >> /'
    else
        echo "回收站路径不存在: $TRASH_PATH"
    fi

    # 第二阶段等待
    echo "等待5分钟（回收站清理完成）..."
    sleep 300
done

echo "全部清理操作完成"
