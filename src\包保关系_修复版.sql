CREATE CATALOG paimon_catalog WITH (
  -- 'type' = 'table-store',
  'type' = 'paimon',
  'warehouse' = 'hdfs:///user/hh_slfn2_sschj/paimon',
  'default-database' = 'ubd_sscj_prod_flink'
);
USE CATALOG paimon_catalog;
CREATE CATALOG hive_catalog WITH (
  'type' = 'hive',
  'default-database' = 'ubd_sscj_prod_flink',
  'hive-conf-dir' = 'hdfs:///user/hh_slfn2_sschj/hive/conf'
);
USE CATALOG hive_catalog;
SET
  'sql-client.execution.result-mode' = 'tableau';
SET
  'yarn.application.queue' = 'hh_slfn2_sschj';
SET
  'table.exec.sink.not-null-enforcer' = 'DROP';
SET
  'table.exec.sink.upsert-materialize' = 'NONE';

-- 新的Kafka表
-- =============================================================================
--  1. 创建 Kafka 目标表 (dwa_r_kafka_bbgx_011)
--     用于存储经过关联和过滤后，需要下发的数据。
--     该表的字段结构根据您提供的"下发数据样例"来定义。
-- =============================================================================
CREATE TABLE IF NOT EXISTS `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_bbgx_011` (
    `id` STRING,
    `user_id` STRING,
    `manager_id` STRING,
    `is_vip` STRING,
    `is_main` STRING,
    `is_recover` STRING,
    `customer_mobile_fnv` STRING,
    `customer_mobile` STRING,
    `update_id` STRING,
    `update_time` STRING,
    `province_code` STRING,
    `create_id` STRING,
    `create_time` STRING
)
WITH (
  'connector' = 'kafka',
  'properties.bootstrap.servers' = '************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095,************:9095',
  'properties.security.protocol' = 'SASL_SSL',
  'properties.sasl.mechanism' = 'SCRAM-SHA-256',
  'properties.sasl.jaas.config' = 'org.apache.kafka.common.security.scram.ScramLoginModule required username="sjzt-sssc-outkafka1" password="sjzt-Sssc#@240910";',
  'properties.ssl.truststore.location' = '/usr/share/sscj/client.truststore.jks',
  'properties.ssl.truststore.password' = 'Sunsd10#',
  'properties.ssl.endpoint.identification.algorithm' = '',
  'topic' = 'CMW_CUST_MANAGER',
  'key.format' = 'json',
  'key.fields' = 'user_id',
  'value.format' = 'json',
  'key.json.ignore-parse-errors' = 'true',
  'key.json.fail-on-missing-field' = 'false',
  'value.json.ignore-parse-errors' = 'true',
  'value.json.fail-on-missing-field' = 'false'
);

-- =============================================================================
--  2. 核心处理逻辑 - 使用Regular Join替代Temporal Join
--     - 以流式方式读取 `ods_r_paimon_cmw_cust_manager_ref` 表的变更数据。
--     - 使用 Regular Join 与 `ods_r_paimon_tf_f_user` 维表进行关联。
--     - 筛选出 `PROVINCE_CODE` 为 '11' 或 '011' 的数据。
--     - 将结果插入到 Kafka 目标表 `dwa_r_kafka_bbgx_011` 中。
-- =============================================================================
INSERT INTO `hive_catalog`.`ubd_sscj_prod_flink`.`dwa_r_kafka_bbgx_011`
SELECT
    ref.id,
    ref.user_id,
    ref.manager_id,
    ref.is_vip,
    ref.is_main,
    ref.is_recover,
    ref.customer_mobile_fnv,
    ref.customer_mobile,
    ref.update_id,
    ref.update_time,
    usr.PROVINCE_CODE AS province_code,
    ref.create_id,
    ref.create_time
FROM
    `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_cmw_cust_manager_ref` AS ref
INNER JOIN
    `paimon_catalog`.`ubd_sscj_prod_flink`.`ods_r_paimon_tf_f_user` AS usr
ON
    ref.user_id = usr.USER_ID
WHERE
    usr.PROVINCE_CODE IN ('11', '011');
